#!/usr/bin/env python3
"""
简单数据库检查
"""

import sqlite3

try:
    print("=== 检查数据库状态 ===")
    
    # 检查fucai3d.db
    conn = sqlite3.connect("data/fucai3d.db")
    cursor = conn.cursor()
    
    # 获取最新3期数据
    cursor.execute("""
        SELECT issue, draw_date, hundreds, tens, units
        FROM lottery_data
        ORDER BY id DESC
        LIMIT 3
    """)
    
    records = cursor.fetchall()
    print("最新3期数据:")
    for record in records:
        issue, draw_date, h, t, u = record
        print(f"期号: {issue}, 开奖: {h}{t}{u}, 日期: {draw_date}")
    
    # 检查2025214期
    cursor.execute("SELECT * FROM lottery_data WHERE issue = '2025214'")
    record_214 = cursor.fetchone()
    
    if record_214:
        print(f"\n2025214期存在: {record_214[2]}{record_214[3]}{record_214[4]}")
    else:
        print("\n2025214期不存在")
    
    # 检查2025215期
    cursor.execute("SELECT * FROM lottery_data WHERE issue = '2025215'")
    record_215 = cursor.fetchone()
    
    if record_215:
        print(f"2025215期存在: {record_215[2]}{record_215[3]}{record_215[4]}")
    else:
        print("2025215期不存在 - 这就是问题所在！")
    
    conn.close()
    
except Exception as e:
    print(f"检查失败: {e}")
