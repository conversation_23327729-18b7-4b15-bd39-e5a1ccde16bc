#!/usr/bin/env python3
"""
分析407预测的历史记录和逻辑
"""

import sqlite3
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def analyze_407_predictions():
    """分析407预测历史"""
    try:
        conn = sqlite3.connect("data/fucai3d.db")
        cursor = conn.cursor()
        
        print("=== 407预测历史记录分析 ===")
        
        # 查找所有407的预测记录
        cursor.execute("""
            SELECT issue, hundreds, tens, units, created_at, fusion_method
            FROM final_predictions 
            WHERE hundreds=4 AND tens=0 AND units=7 
            ORDER BY created_at DESC 
            LIMIT 20
        """)
        
        predictions_407 = cursor.fetchall()
        
        if predictions_407:
            print(f"找到 {len(predictions_407)} 条407预测记录:")
            for row in predictions_407:
                issue, hundreds, tens, units, created_at, fusion_method = row
                print(f"期号: {issue}, 预测: {hundreds}{tens}{units}, 方法: {fusion_method}, 时间: {created_at}")
        else:
            print("没有找到407的预测记录")
        
        # 分析最近的预测分布
        print("\n=== 最近预测号码分布 ===")
        cursor.execute("""
            SELECT hundreds, tens, units, COUNT(*) as count
            FROM final_predictions 
            WHERE created_at >= date('now', '-7 days')
            GROUP BY hundreds, tens, units
            ORDER BY count DESC, created_at DESC
            LIMIT 10
        """)
        
        recent_predictions = cursor.fetchall()
        for row in recent_predictions:
            hundreds, tens, units, count = row
            print(f"预测: {hundreds}{tens}{units}, 出现次数: {count}")
        
        # 检查期号变化情况
        print("\n=== 期号变化检查 ===")
        cursor.execute("""
            SELECT DISTINCT issue, COUNT(*) as prediction_count, MIN(created_at) as first_time, MAX(created_at) as last_time
            FROM final_predictions 
            WHERE created_at >= date('now', '-3 days')
            GROUP BY issue
            ORDER BY issue DESC
        """)
        
        issue_analysis = cursor.fetchall()
        for row in issue_analysis:
            issue, count, first_time, last_time = row
            print(f"期号: {issue}, 预测数量: {count}, 首次: {first_time}, 最后: {last_time}")
        
        # 检查智能推荐的种子逻辑
        print("\n=== 智能推荐种子分析 ===")
        from src.fusion.smart_recommendation_engine import SmartRecommendationEngine
        
        engine = SmartRecommendationEngine()
        issue_info = engine._get_next_issue_info()
        
        print(f"当前期号信息: {issue_info}")
        
        # 模拟种子计算
        import hashlib
        current_time = datetime.now()
        current_issue = issue_info.get('issue', current_time.strftime('%Y%j'))
        
        for position in ['hundreds', 'tens', 'units']:
            seed_string = f"{current_issue}_{position}_{current_time.strftime('%Y%m%d')}"
            dynamic_seed = int(hashlib.md5(seed_string.encode()).hexdigest()[:8], 16)
            print(f"位置 {position}: 种子字符串='{seed_string}', 动态种子={dynamic_seed}")
        
        conn.close()
        
    except Exception as e:
        print(f"分析407预测历史时出错: {e}")
        import traceback
        traceback.print_exc()

def test_prediction_logic():
    """测试预测逻辑的变化性"""
    print("\n=== 测试预测逻辑变化性 ===")
    
    try:
        from src.fusion.smart_recommendation_engine import SmartRecommendationEngine
        
        engine = SmartRecommendationEngine()
        
        # 测试连续调用是否产生相同结果
        print("连续3次调用智能推荐:")
        for i in range(3):
            result = engine.generate_daily_recommendations()
            combination = result.get('combination', {}).get('combination', 'N/A')
            confidence = result.get('overall_confidence', 0)
            print(f"第{i+1}次: 组合={combination}, 置信度={confidence:.3f}")
        
        # 检查期号是否正确变化
        issue_info = engine._get_next_issue_info()
        print(f"\n期号信息: {issue_info}")
        
    except Exception as e:
        print(f"测试预测逻辑时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_407_predictions()
    test_prediction_logic()
