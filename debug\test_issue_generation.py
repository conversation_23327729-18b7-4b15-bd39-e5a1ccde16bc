#!/usr/bin/env python3
"""
测试期号生成逻辑
"""

import sqlite3
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_issue_generation():
    """测试期号生成逻辑"""
    
    print("=== 测试期号生成逻辑 ===")
    
    # 1. 检查数据库中的最新期号
    try:
        conn = sqlite3.connect("data/fucai3d.db")
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT issue, draw_date, hundreds, tens, units
            FROM lottery_data
            ORDER BY id DESC
            LIMIT 5
        """)
        
        records = cursor.fetchall()
        print("数据库中最新5期数据:")
        for record in records:
            issue, draw_date, h, t, u = record
            print(f"期号: {issue}, 开奖: {h}{t}{u}, 日期: {draw_date}")
        
        if records:
            latest_issue = records[0][0]
            latest_date = records[0][1]
            print(f"\n最新期号: {latest_issue}, 最新日期: {latest_date}")
        else:
            print("数据库中没有数据")
            return
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库失败: {e}")
        return
    
    # 2. 测试SmartRecommendationEngine的期号生成
    try:
        from src.fusion.smart_recommendation_engine import SmartRecommendationEngine
        
        engine = SmartRecommendationEngine()
        
        # 测试期号信息获取
        issue_info = engine._get_next_issue_info()
        print(f"\n期号信息: {issue_info}")
        
        # 测试期号计算逻辑
        next_issue, next_date = engine._calculate_next_issue(latest_issue, latest_date)
        print(f"计算的下一期: {next_issue}, 日期: {next_date}")
        
        # 分析期号逻辑
        if latest_issue and len(latest_issue) >= 7:
            issue_year = int(latest_issue[:4])
            issue_number = int(latest_issue[4:])
            current_year = datetime.now().year
            
            print(f"\n期号分析:")
            print(f"最新期号年份: {issue_year}")
            print(f"最新期号序号: {issue_number}")
            print(f"当前年份: {current_year}")
            print(f"年份差: {current_year - issue_year}")
            
            # 检查是否是2025214期
            if latest_issue == "2025214":
                print(f"\n发现2025214期，开奖号码: {records[0][2]}{records[0][3]}{records[0][4]}")
                print("这说明2025214期已经开奖，下一期应该是2025215")
                
                # 检查是否有2025215期的数据
                conn = sqlite3.connect("data/fucai3d.db")
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM lottery_data WHERE issue = '2025215'")
                result_215 = cursor.fetchone()
                
                if result_215:
                    print("数据库中已有2025215期数据")
                else:
                    print("数据库中没有2025215期数据，这就是问题所在！")
                
                conn.close()
        
    except Exception as e:
        print(f"测试SmartRecommendationEngine失败: {e}")
        import traceback
        traceback.print_exc()

def simulate_data_update():
    """模拟数据更新"""
    print("\n=== 模拟数据更新 ===")
    
    try:
        # 模拟2025215期数据
        conn = sqlite3.connect("data/fucai3d.db")
        cursor = conn.cursor()
        
        # 检查2025215期是否存在
        cursor.execute("SELECT * FROM lottery_data WHERE issue = '2025215'")
        exists = cursor.fetchone()
        
        if not exists:
            print("插入模拟的2025215期数据...")
            cursor.execute("""
                INSERT INTO lottery_data (
                    issue, draw_date, hundreds, tens, units,
                    sum_value, span, number_type, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                "2025215", "2025-08-13", 1, 2, 3,  # 模拟开奖号码123
                6, 2, "组六", 
                datetime.now().isoformat(), datetime.now().isoformat()
            ))
            
            conn.commit()
            print("模拟数据插入成功")
        else:
            print("2025215期数据已存在")
        
        conn.close()
        
        # 重新测试期号生成
        print("\n重新测试期号生成:")
        from src.fusion.smart_recommendation_engine import SmartRecommendationEngine
        engine = SmartRecommendationEngine()
        issue_info = engine._get_next_issue_info()
        print(f"更新后的期号信息: {issue_info}")
        
    except Exception as e:
        print(f"模拟数据更新失败: {e}")

if __name__ == "__main__":
    test_issue_generation()
    simulate_data_update()
