#!/usr/bin/env python3
"""
简单检查407预测记录
"""

import sqlite3

def check_407_records():
    """检查407预测记录"""
    try:
        conn = sqlite3.connect("data/fucai3d.db")
        cursor = conn.cursor()
        
        print("=== 407预测记录检查 ===")
        
        # 查找407预测记录
        cursor.execute("""
            SELECT issue, created_at, fusion_method
            FROM final_predictions 
            WHERE hundreds=4 AND tens=0 AND units=7 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        records = cursor.fetchall()
        
        if records:
            print(f"找到 {len(records)} 条407预测记录:")
            for issue, created_at, fusion_method in records:
                print(f"期号: {issue}, 时间: {created_at}, 方法: {fusion_method}")
        else:
            print("没有找到407预测记录")
        
        # 检查最近的预测分布
        print("\n=== 最近预测分布 ===")
        cursor.execute("""
            SELECT issue, hundreds, tens, units, created_at
            FROM final_predictions 
            ORDER BY created_at DESC 
            LIMIT 20
        """)
        
        recent = cursor.fetchall()
        for issue, h, t, u, created_at in recent:
            print(f"期号: {issue}, 预测: {h}{t}{u}, 时间: {created_at}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查出错: {e}")

if __name__ == "__main__":
    check_407_records()
