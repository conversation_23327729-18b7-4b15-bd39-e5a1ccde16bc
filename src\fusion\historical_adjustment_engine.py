#!/usr/bin/env python3
"""
历史数据驱动调整引擎
基于8,367条真实历史数据的确定性调整算法，完全替换随机因子
"""

import sqlite3
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
import json
import os
from pathlib import Path
import hashlib
import math

logger = logging.getLogger(__name__)


class HistoricalAdjustmentEngine:
    """
    历史数据驱动调整引擎
    
    基于8,367条真实历史数据，提供完全确定性的调整算法，
    严格禁止任何随机因子，确保一切预测都有迹可循。
    """
    
    def __init__(self, db_path: str = "data/fucai3d.db"):
        """
        初始化历史数据驱动调整引擎
        
        Args:
            db_path: 数据库路径
        """
        # 确保使用绝对路径
        if not os.path.isabs(db_path):
            current_dir = Path(__file__).parent.parent.parent
            self.db_path = str(current_dir / db_path)
        else:
            self.db_path = db_path
            
        # 缓存历史数据分析结果
        self._historical_cache = {}
        self._frequency_cache = {}
        self._pattern_cache = {}
        
        # 初始化历史数据
        self._load_historical_data()
        
        logger.info("历史数据驱动调整引擎初始化完成")
    
    def _load_historical_data(self):
        """加载并缓存历史数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 加载所有历史数据
                query = """
                    SELECT issue, draw_date, hundreds, tens, units, sum_value, span, number_type
                    FROM lottery_data
                    ORDER BY id ASC
                """
                self.historical_df = pd.read_sql_query(query, conn)
                
                if self.historical_df.empty:
                    logger.warning("数据库中没有历史数据")
                    return
                
                # 预计算频率分析
                self._precompute_frequency_analysis()
                
                # 预计算周期性分析
                self._precompute_cyclical_analysis()
                
                # 预计算关联性分析
                self._precompute_correlation_analysis()
                
                logger.info(f"加载历史数据成功: {len(self.historical_df)} 条记录")
                
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            self.historical_df = pd.DataFrame()
    
    def _precompute_frequency_analysis(self):
        """预计算频率分析"""
        if self.historical_df.empty:
            return
            
        # 计算各位置数字频率
        for position in ['hundreds', 'tens', 'units']:
            if position in self.historical_df.columns:
                freq_dist = self.historical_df[position].value_counts(normalize=True).to_dict()
                self._frequency_cache[position] = freq_dist
                
                # 计算期望频率（理论上每个数字应该是0.1）
                expected_freq = 0.1
                deviation_scores = {}
                for digit in range(10):
                    actual_freq = freq_dist.get(digit, 0)
                    # 计算偏差分数：负值表示出现不足，正值表示出现过多
                    deviation_scores[digit] = (actual_freq - expected_freq) / expected_freq
                
                self._frequency_cache[f"{position}_deviation"] = deviation_scores
    
    def _precompute_cyclical_analysis(self):
        """预计算周期性分析"""
        if self.historical_df.empty:
            return
            
        # 分析不同时间周期的数字分布
        self.historical_df['draw_date'] = pd.to_datetime(self.historical_df['draw_date'])
        
        # 按月份分析
        self.historical_df['month'] = self.historical_df['draw_date'].dt.month
        monthly_patterns = {}
        for position in ['hundreds', 'tens', 'units']:
            if position in self.historical_df.columns:
                monthly_dist = {}
                for month in range(1, 13):
                    month_data = self.historical_df[self.historical_df['month'] == month]
                    if not month_data.empty:
                        freq_dist = month_data[position].value_counts(normalize=True).to_dict()
                        monthly_dist[month] = freq_dist
                monthly_patterns[position] = monthly_dist
        
        self._pattern_cache['monthly'] = monthly_patterns
        
        # 按星期分析
        self.historical_df['weekday'] = self.historical_df['draw_date'].dt.weekday
        weekly_patterns = {}
        for position in ['hundreds', 'tens', 'units']:
            if position in self.historical_df.columns:
                weekly_dist = {}
                for weekday in range(7):
                    week_data = self.historical_df[self.historical_df['weekday'] == weekday]
                    if not week_data.empty:
                        freq_dist = week_data[position].value_counts(normalize=True).to_dict()
                        weekly_dist[weekday] = freq_dist
                weekly_patterns[position] = weekly_dist
        
        self._pattern_cache['weekly'] = weekly_patterns
    
    def _precompute_correlation_analysis(self):
        """预计算关联性分析"""
        if self.historical_df.empty:
            return
            
        # 计算位置间的关联性
        position_correlations = {}
        
        # 百位与十位的关联
        if 'hundreds' in self.historical_df.columns and 'tens' in self.historical_df.columns:
            correlation_matrix = {}
            for h in range(10):
                h_data = self.historical_df[self.historical_df['hundreds'] == h]
                if not h_data.empty:
                    tens_dist = h_data['tens'].value_counts(normalize=True).to_dict()
                    correlation_matrix[h] = tens_dist
            position_correlations['hundreds_tens'] = correlation_matrix
        
        # 十位与个位的关联
        if 'tens' in self.historical_df.columns and 'units' in self.historical_df.columns:
            correlation_matrix = {}
            for t in range(10):
                t_data = self.historical_df[self.historical_df['tens'] == t]
                if not t_data.empty:
                    units_dist = t_data['units'].value_counts(normalize=True).to_dict()
                    correlation_matrix[t] = units_dist
            position_correlations['tens_units'] = correlation_matrix
        
        # 百位与个位的关联
        if 'hundreds' in self.historical_df.columns and 'units' in self.historical_df.columns:
            correlation_matrix = {}
            for h in range(10):
                h_data = self.historical_df[self.historical_df['hundreds'] == h]
                if not h_data.empty:
                    units_dist = h_data['units'].value_counts(normalize=True).to_dict()
                    correlation_matrix[h] = units_dist
            position_correlations['hundreds_units'] = correlation_matrix
        
        self._pattern_cache['correlations'] = position_correlations
    
    def calculate_issue_sensitivity_adjustment(self, number: int, issue: str, position: str) -> float:
        """
        基于期号特性计算调整系数
        
        Args:
            number: 数字 (0-9)
            issue: 期号
            position: 位置 ('hundreds', 'tens', 'units')
            
        Returns:
            float: 调整系数 (-0.1 到 0.1)
        """
        try:
            if not issue or len(issue) < 4:
                return 0.0
            
            # 提取期号的数学特性
            year = int(issue[:4]) if len(issue) >= 4 else 2025
            day_part = int(issue[4:]) if len(issue) > 4 else 1
            
            adjustment = 0.0
            
            # 基于期号尾数的调整
            issue_last_digit = int(issue[-1]) if issue else 0
            if number == issue_last_digit:
                adjustment += 0.02
            
            # 基于期号数字和的调整
            issue_sum = sum(int(d) for d in issue if d.isdigit())
            if number == (issue_sum % 10):
                adjustment += 0.015
            
            # 基于年份特性的调整
            year_last_digit = year % 10
            if number == year_last_digit:
                adjustment += 0.01
            
            # 基于天数特性的调整
            day_mod = day_part % 10
            if number == day_mod:
                adjustment += 0.01
            
            # 确保调整系数在合理范围内
            return max(-0.1, min(0.1, adjustment))
            
        except Exception as e:
            logger.error(f"计算期号敏感性调整失败: {e}")
            return 0.0
    
    def calculate_frequency_deviation_adjustment(self, number: int, position: str) -> float:
        """
        基于历史频率偏差计算调整系数
        
        Args:
            number: 数字 (0-9)
            position: 位置 ('hundreds', 'tens', 'units')
            
        Returns:
            float: 调整系数 (-0.15 到 0.15)
        """
        try:
            deviation_key = f"{position}_deviation"
            if deviation_key not in self._frequency_cache:
                return 0.0
            
            deviation_scores = self._frequency_cache[deviation_key]
            deviation = deviation_scores.get(number, 0.0)
            
            # 如果数字出现不足（负偏差），给予正向调整
            # 如果数字出现过多（正偏差），给予负向调整
            adjustment = -deviation * 0.15  # 反向调整，平衡频率
            
            return max(-0.15, min(0.15, adjustment))
            
        except Exception as e:
            logger.error(f"计算频率偏差调整失败: {e}")
            return 0.0

    def calculate_cyclical_pattern_adjustment(self, number: int, current_time: datetime, position: str) -> float:
        """
        基于周期性模式计算调整系数

        Args:
            number: 数字 (0-9)
            current_time: 当前时间
            position: 位置 ('hundreds', 'tens', 'units')

        Returns:
            float: 调整系数 (-0.1 到 0.1)
        """
        try:
            adjustment = 0.0

            # 月份模式调整
            if 'monthly' in self._pattern_cache:
                monthly_patterns = self._pattern_cache['monthly'].get(position, {})
                current_month = current_time.month
                month_dist = monthly_patterns.get(current_month, {})

                if month_dist:
                    expected_freq = 0.1
                    actual_freq = month_dist.get(number, expected_freq)
                    # 基于月份模式的调整
                    adjustment += (actual_freq - expected_freq) * 0.5

            # 星期模式调整
            if 'weekly' in self._pattern_cache:
                weekly_patterns = self._pattern_cache['weekly'].get(position, {})
                current_weekday = current_time.weekday()
                week_dist = weekly_patterns.get(current_weekday, {})

                if week_dist:
                    expected_freq = 0.1
                    actual_freq = week_dist.get(number, expected_freq)
                    # 基于星期模式的调整
                    adjustment += (actual_freq - expected_freq) * 0.3

            return max(-0.1, min(0.1, adjustment))

        except Exception as e:
            logger.error(f"计算周期性调整失败: {e}")
            return 0.0

    def calculate_correlation_adjustment(self, number: int, position: str, context: Dict[str, Any]) -> float:
        """
        基于位置关联性计算调整系数

        Args:
            number: 数字 (0-9)
            position: 位置 ('hundreds', 'tens', 'units')
            context: 上下文信息，包含其他位置的推荐

        Returns:
            float: 调整系数 (-0.1 到 0.1)
        """
        try:
            if 'correlations' not in self._pattern_cache:
                return 0.0

            correlations = self._pattern_cache['correlations']
            adjustment = 0.0

            # 根据位置计算关联性调整
            if position == 'tens':
                # 十位基于百位的关联性
                hundreds_rec = context.get('hundreds_recommendation')
                if hundreds_rec and 'hundreds_tens' in correlations:
                    hundreds_num = hundreds_rec.get('number')
                    if hundreds_num is not None:
                        correlation_matrix = correlations['hundreds_tens']
                        tens_dist = correlation_matrix.get(hundreds_num, {})
                        expected_freq = 0.1
                        actual_freq = tens_dist.get(number, expected_freq)
                        adjustment += (actual_freq - expected_freq) * 0.8

            elif position == 'units':
                # 个位基于十位的关联性
                tens_rec = context.get('tens_recommendation')
                if tens_rec and 'tens_units' in correlations:
                    tens_num = tens_rec.get('number')
                    if tens_num is not None:
                        correlation_matrix = correlations['tens_units']
                        units_dist = correlation_matrix.get(tens_num, {})
                        expected_freq = 0.1
                        actual_freq = units_dist.get(number, expected_freq)
                        adjustment += (actual_freq - expected_freq) * 0.8

                # 个位基于百位的关联性
                hundreds_rec = context.get('hundreds_recommendation')
                if hundreds_rec and 'hundreds_units' in correlations:
                    hundreds_num = hundreds_rec.get('number')
                    if hundreds_num is not None:
                        correlation_matrix = correlations['hundreds_units']
                        units_dist = correlation_matrix.get(hundreds_num, {})
                        expected_freq = 0.1
                        actual_freq = units_dist.get(number, expected_freq)
                        adjustment += (actual_freq - expected_freq) * 0.4

            return max(-0.1, min(0.1, adjustment))

        except Exception as e:
            logger.error(f"计算关联性调整失败: {e}")
            return 0.0

    def calculate_pattern_matching_adjustment(self, number: int, position: str, historical_patterns: List[Dict]) -> float:
        """
        基于历史模式匹配计算调整系数

        Args:
            number: 数字 (0-9)
            position: 位置 ('hundreds', 'tens', 'units')
            historical_patterns: 历史模式列表

        Returns:
            float: 调整系数 (-0.08 到 0.08)
        """
        try:
            if self.historical_df.empty or not historical_patterns:
                return 0.0

            adjustment = 0.0
            pattern_count = 0

            # 分析最近的历史模式
            recent_data = self.historical_df.tail(50)  # 最近50期

            for pattern in historical_patterns[:5]:  # 最多考虑5个模式
                pattern_type = pattern.get('type', '')
                pattern_value = pattern.get('value')

                if pattern_type == 'consecutive_absence':
                    # 连续未出现模式
                    if pattern_value and pattern_value.get('digit') == number:
                        absence_count = pattern_value.get('count', 0)
                        # 连续未出现越久，给予越高的正向调整
                        if absence_count > 10:
                            adjustment += 0.03
                        elif absence_count > 5:
                            adjustment += 0.02
                        pattern_count += 1

                elif pattern_type == 'hot_number':
                    # 热号模式
                    if pattern_value and pattern_value.get('digit') == number:
                        hot_score = pattern_value.get('score', 0)
                        # 热号给予适度正向调整
                        adjustment += min(0.02, hot_score * 0.01)
                        pattern_count += 1

                elif pattern_type == 'cold_number':
                    # 冷号模式
                    if pattern_value and pattern_value.get('digit') == number:
                        cold_score = pattern_value.get('score', 0)
                        # 冷号给予补偿性正向调整
                        adjustment += min(0.025, cold_score * 0.012)
                        pattern_count += 1

            # 平均化调整
            if pattern_count > 0:
                adjustment = adjustment / pattern_count

            return max(-0.08, min(0.08, adjustment))

        except Exception as e:
            logger.error(f"计算模式匹配调整失败: {e}")
            return 0.0

    def get_comprehensive_adjustment(self, number: int, position: str, issue: str,
                                   current_time: datetime, context: Dict[str, Any],
                                   historical_patterns: List[Dict]) -> float:
        """
        获取综合调整系数

        Args:
            number: 数字 (0-9)
            position: 位置 ('hundreds', 'tens', 'units')
            issue: 期号
            current_time: 当前时间
            context: 上下文信息
            historical_patterns: 历史模式

        Returns:
            float: 综合调整系数
        """
        try:
            # 计算各种调整系数
            issue_adj = self.calculate_issue_sensitivity_adjustment(number, issue, position)
            freq_adj = self.calculate_frequency_deviation_adjustment(number, position)
            cyclical_adj = self.calculate_cyclical_pattern_adjustment(number, current_time, position)
            correlation_adj = self.calculate_correlation_adjustment(number, position, context)
            pattern_adj = self.calculate_pattern_matching_adjustment(number, position, historical_patterns)

            # 加权综合调整
            comprehensive_adj = (
                issue_adj * 0.15 +           # 期号敏感性权重15%
                freq_adj * 0.35 +            # 频率偏差权重35%
                cyclical_adj * 0.20 +        # 周期性权重20%
                correlation_adj * 0.20 +     # 关联性权重20%
                pattern_adj * 0.10           # 模式匹配权重10%
            )

            # 确保调整系数在合理范围内
            return max(-0.2, min(0.2, comprehensive_adj))

        except Exception as e:
            logger.error(f"计算综合调整失败: {e}")
            return 0.0

    def get_historical_evidence(self, number: int, position: str, issue: str) -> Dict[str, Any]:
        """
        获取数字推荐的历史证据

        Args:
            number: 数字 (0-9)
            position: 位置 ('hundreds', 'tens', 'units')
            issue: 期号

        Returns:
            Dict: 历史证据信息
        """
        try:
            evidence = {
                'frequency_analysis': {},
                'recent_performance': {},
                'pattern_analysis': {},
                'correlation_analysis': {}
            }

            # 频率分析证据
            if position in self._frequency_cache:
                freq_dist = self._frequency_cache[position]
                actual_freq = freq_dist.get(number, 0)
                evidence['frequency_analysis'] = {
                    'actual_frequency': actual_freq,
                    'expected_frequency': 0.1,
                    'deviation': actual_freq - 0.1,
                    'rank': sorted(freq_dist.values(), reverse=True).index(actual_freq) + 1
                }

            # 最近表现证据
            if not self.historical_df.empty:
                recent_data = self.historical_df.tail(30)  # 最近30期
                recent_count = len(recent_data[recent_data[position] == number])
                evidence['recent_performance'] = {
                    'recent_30_appearances': recent_count,
                    'recent_frequency': recent_count / 30 if len(recent_data) > 0 else 0
                }

            return evidence

        except Exception as e:
            logger.error(f"获取历史证据失败: {e}")
            return {}
