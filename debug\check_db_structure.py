#!/usr/bin/env python3
"""
数据库结构检查脚本
检查final_predictions表结构和数据
"""

import sqlite3
import os

def check_database_structure():
    """检查数据库结构"""
    db_path = "data/fucai3d.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查final_predictions表结构
        print("=== final_predictions表结构 ===")
        cursor.execute("PRAGMA table_info(final_predictions)")
        columns = cursor.fetchall()

        for col in columns:
            cid, name, type_, notnull, default, pk = col
            default_str = f"DEFAULT: {default}" if default else ""
            print(f"{name:20} {type_:15} {'NOT NULL' if notnull else '':10} {'PRIMARY KEY' if pk else ''} {default_str}")
        
        # 检查表中的数据量
        cursor.execute("SELECT COUNT(*) FROM final_predictions")
        count = cursor.fetchone()[0]
        print(f"\n数据总量: {count} 条")
        
        # 检查最新的几条数据
        print("\n=== 最新5条数据 ===")
        cursor.execute("""
            SELECT issue, hundreds, tens, units, confidence_level, created_at 
            FROM final_predictions 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        recent_data = cursor.fetchall()
        for row in recent_data:
            issue, hundreds, tens, units, confidence, created_at = row
            print(f"期号: {issue}, 预测: {hundreds}{tens}{units}, 置信度: {confidence}, 时间: {created_at}")
        
        # 检查置信度字段的数据类型和值
        print("\n=== 置信度字段分析 ===")
        cursor.execute("""
            SELECT confidence_level, COUNT(*) as count
            FROM final_predictions 
            GROUP BY confidence_level 
            ORDER BY count DESC
        """)
        
        confidence_stats = cursor.fetchall()
        for conf, count in confidence_stats:
            print(f"置信度值: {conf} (类型: {type(conf).__name__}), 数量: {count}")
        
        # 检查是否有prediction_records表
        print("\n=== 检查预测复盘相关表 ===")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%prediction%'")
        prediction_tables = cursor.fetchall()

        for table in prediction_tables:
            print(f"表名: {table[0]}")

        # 检查learning_prediction_records表结构
        print("\n=== learning_prediction_records表结构 ===")
        try:
            cursor.execute("PRAGMA table_info(learning_prediction_records)")
            columns = cursor.fetchall()

            for col in columns:
                cid, name, type_, notnull, default, pk = col
                print(f"{name:20} {type_:15} {'NOT NULL' if notnull else '':10} {'PRIMARY KEY' if pk else ''}")

            # 检查表中的数据量
            cursor.execute("SELECT COUNT(*) FROM learning_prediction_records")
            count = cursor.fetchone()[0]
            print(f"\n复盘数据总量: {count} 条")

        except Exception as e:
            print(f"检查learning_prediction_records表失败: {e}")

        conn.close()
        
    except Exception as e:
        print(f"检查数据库时出错: {e}")

if __name__ == "__main__":
    check_database_structure()
