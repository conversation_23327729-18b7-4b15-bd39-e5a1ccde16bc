"""
用户反馈管理API路由
提供反馈收集、满意度调查、反馈分析等接口
"""

from fastapi import APIRouter, HTTPException, Query, Body
from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel
import logging
import json
import sqlite3

logger = logging.getLogger(__name__)

try:
    from ...database.feedback_data_manager import feedback_data_manager, UserFeedback, SatisfactionSurvey, UserPreference
    from ...analytics.feedback_analytics import feedback_analytics
    from ...core.auto_optimization_engine import auto_optimization_engine
    from ...core.auto_optimization_scheduler import auto_optimization_scheduler
except ImportError:
    from src.database.feedback_data_manager import feedback_data_manager, UserF<PERSON>back, SatisfactionSurvey, UserPreference
    from src.analytics.feedback_analytics import feedback_analytics
    from src.core.auto_optimization_engine import auto_optimization_engine
    from src.core.auto_optimization_scheduler import auto_optimization_scheduler

router = APIRouter(prefix="/api/feedback", tags=["用户反馈"])

class FeedbackRequest(BaseModel):
    """反馈提交请求模型"""
    user_id: str
    feedback_type: str
    content: str
    rating: Optional[int] = None
    prediction_period: Optional[str] = None
    prediction_numbers: Optional[str] = None
    actual_numbers: Optional[str] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None

class SatisfactionSurveyRequest(BaseModel):
    """满意度调查请求模型"""
    user_id: str
    overall_satisfaction: int
    prediction_accuracy_rating: int
    interface_usability_rating: int
    feature_completeness_rating: int
    recommendation_quality_rating: int
    likelihood_to_recommend: int
    comments: Optional[str] = None
    survey_version: str = "v1.0"

class UserPreferenceRequest(BaseModel):
    """用户偏好请求模型"""
    user_id: str
    preference_type: str
    preference_key: str
    preference_value: Any
    confidence_score: float = 0.0
    source: str = "explicit"

@router.post("/submit")
async def submit_feedback(request: FeedbackRequest):
    """提交用户反馈"""
    try:
        feedback = UserFeedback(
            feedback_id="",  # 将自动生成
            user_id=request.user_id,
            feedback_type=request.feedback_type,
            content=request.content,
            rating=request.rating,
            prediction_period=request.prediction_period,
            prediction_numbers=request.prediction_numbers,
            actual_numbers=request.actual_numbers,
            tags=request.tags,
            metadata=request.metadata
        )
        
        feedback_id = feedback_data_manager.create_feedback(feedback)

        # 🆕 立即触发智能反馈处理 - 解决闭环断裂问题
        optimization_result = None
        if request.content:
            try:
                logger.info(f"触发智能反馈处理: {request.content[:50]}...")
                optimization_result = auto_optimization_engine.process_intelligent_feedback(
                    request.content
                )
                logger.info(f"智能反馈处理完成: {optimization_result.get('total_actions_executed', 0)}个行动")
            except Exception as e:
                logger.error(f"智能反馈处理失败: {e}")
                # 不影响反馈提交，只记录错误

        return {
            "status": "success",
            "message": "反馈提交成功，系统正在自动优化" if optimization_result else "反馈提交成功",
            "data": {
                "feedback_id": feedback_id,
                "submitted_at": datetime.now().isoformat(),
                # 🆕 优化执行状态
                "optimization_status": {
                    "triggered": optimization_result is not None,
                    "actions_executed": optimization_result.get('total_actions_executed', 0) if optimization_result else 0,
                    "successful_actions": optimization_result.get('successful_actions', 0) if optimization_result else 0,
                    "message": "系统正在基于您的反馈自动优化" if optimization_result else "反馈已记录"
                }
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"提交反馈失败: {str(e)}")

@router.post("/quick")
async def submit_quick_feedback(
    user_id: str = Body(...),
    feedback_type: str = Body(...),
    content: str = Body(...),
    rating: int = Body(...)
):
    """提交快速反馈"""
    try:
        feedback = UserFeedback(
            feedback_id="",
            user_id=user_id,
            feedback_type=feedback_type,
            content=content,
            rating=rating,
            tags=["quick_feedback"]
        )
        
        feedback_id = feedback_data_manager.create_feedback(feedback)
        
        return {
            "status": "success",
            "message": "快速反馈提交成功",
            "data": {"feedback_id": feedback_id}
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"提交快速反馈失败: {str(e)}")

@router.post("/satisfaction-survey")
async def submit_satisfaction_survey(request: SatisfactionSurveyRequest):
    """提交满意度调查"""
    try:
        survey = SatisfactionSurvey(
            survey_id="",  # 将自动生成
            user_id=request.user_id,
            overall_satisfaction=request.overall_satisfaction,
            prediction_accuracy_rating=request.prediction_accuracy_rating,
            interface_usability_rating=request.interface_usability_rating,
            feature_completeness_rating=request.feature_completeness_rating,
            recommendation_quality_rating=request.recommendation_quality_rating,
            likelihood_to_recommend=request.likelihood_to_recommend,
            comments=request.comments,
            survey_version=request.survey_version
        )
        
        survey_id = feedback_data_manager.create_satisfaction_survey(survey)
        
        return {
            "status": "success",
            "message": "满意度调查提交成功",
            "data": {
                "survey_id": survey_id,
                "submitted_at": datetime.now().isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"提交满意度调查失败: {str(e)}")

@router.get("/user-feedback")
async def get_user_feedback(
    user_id: Optional[str] = Query(None, description="用户ID"),
    feedback_type: Optional[str] = Query(None, description="反馈类型"),
    limit: int = Query(20, description="返回数量限制"),
    offset: int = Query(0, description="偏移量")
):
    """获取用户反馈"""
    try:
        feedback_list = feedback_data_manager.get_user_feedback(
            user_id=user_id,
            feedback_type=feedback_type,
            limit=limit,
            offset=offset
        )
        
        return {
            "status": "success",
            "data": feedback_list,
            "count": len(feedback_list)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户反馈失败: {str(e)}")

@router.get("/statistics")
async def get_feedback_statistics(days: int = Query(30, description="统计天数")):
    """获取反馈统计信息"""
    try:
        stats = feedback_data_manager.get_feedback_statistics(days=days)
        
        return {
            "status": "success",
            "data": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取反馈统计失败: {str(e)}")

@router.get("/nps-score")
async def get_nps_score(days: int = Query(30, description="统计天数")):
    """获取NPS评分"""
    try:
        nps_data = feedback_data_manager.calculate_nps_score(days=days)
        
        return {
            "status": "success",
            "data": nps_data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取NPS评分失败: {str(e)}")

@router.get("/top-issues")
async def get_top_issues(limit: int = Query(10, description="返回数量")):
    """获取热门问题"""
    try:
        issues = feedback_data_manager.get_top_issues(limit=limit)
        
        return {
            "status": "success",
            "data": issues,
            "count": len(issues)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取热门问题失败: {str(e)}")

@router.get("/trends")
async def get_feedback_trends(
    days: int = Query(30, description="分析天数"),
    granularity: str = Query("daily", description="时间粒度: daily/weekly/monthly")
):
    """获取反馈趋势分析"""
    try:
        trends = feedback_analytics.analyze_feedback_trends(days=days, granularity=granularity)
        
        return {
            "status": "success",
            "data": [trend.__dict__ for trend in trends],
            "count": len(trends)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取反馈趋势失败: {str(e)}")

@router.get("/priority-issues")
async def get_priority_issues(limit: int = Query(20, description="返回数量")):
    """获取优先级问题分析"""
    try:
        issues = feedback_analytics.prioritize_issues(limit=limit)
        
        return {
            "status": "success",
            "data": [issue.__dict__ for issue in issues],
            "count": len(issues)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取优先级问题失败: {str(e)}")

@router.get("/satisfaction-analysis")
async def get_satisfaction_analysis(user_id: Optional[str] = Query(None, description="用户ID")):
    """获取满意度分析"""
    try:
        analysis = feedback_analytics.analyze_user_satisfaction(user_id=user_id)
        
        return {
            "status": "success",
            "data": analysis
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取满意度分析失败: {str(e)}")

@router.get("/insights")
async def get_feedback_insights():
    """获取反馈洞察报告"""
    try:
        insights = feedback_analytics.generate_feedback_insights()
        
        return {
            "status": "success",
            "data": insights
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取反馈洞察失败: {str(e)}")

@router.post("/user-preference")
async def set_user_preference(request: UserPreferenceRequest):
    """设置用户偏好"""
    try:
        preference = UserPreference(
            user_id=request.user_id,
            preference_type=request.preference_type,
            preference_key=request.preference_key,
            preference_value=request.preference_value,
            confidence_score=request.confidence_score,
            source=request.source
        )
        
        feedback_data_manager.set_user_preference(preference)
        
        return {
            "status": "success",
            "message": "用户偏好设置成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"设置用户偏好失败: {str(e)}")

@router.get("/user-preferences/{user_id}")
async def get_user_preferences(user_id: str):
    """获取用户偏好"""
    try:
        preferences = feedback_data_manager.get_user_preferences(user_id)
        
        return {
            "status": "success",
            "data": preferences
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户偏好失败: {str(e)}")

@router.get("/survey-stats")
async def get_survey_stats():
    """获取调查统计"""
    try:
        # 获取基础统计
        stats = feedback_data_manager.get_feedback_statistics(days=30)
        nps_data = feedback_data_manager.calculate_nps_score(days=30)
        
        # 计算完成率（简化版本）
        completion_rate = 85.0  # 实际项目中应该从数据库计算
        
        survey_stats = {
            "total_surveys": nps_data.get("total_responses", 0),
            "avg_nps": nps_data.get("nps_score", 0),
            "avg_satisfaction": stats.get("satisfaction_stats", {}).get("avg_satisfaction", 0) or 0,
            "completion_rate": completion_rate
        }
        
        return {
            "status": "success",
            "data": survey_stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取调查统计失败: {str(e)}")

@router.post("/predict-impact")
async def predict_satisfaction_impact(proposed_changes: List[str] = Body(...)):
    """预测改进措施的满意度影响"""
    try:
        impact_prediction = feedback_analytics.predict_satisfaction_impact(proposed_changes)
        
        return {
            "status": "success",
            "data": impact_prediction
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"预测满意度影响失败: {str(e)}")

@router.get("/dashboard")
async def get_feedback_dashboard():
    """获取反馈管理仪表板"""
    try:
        # 获取各种统计数据
        stats = feedback_data_manager.get_feedback_statistics(days=30)
        nps_data = feedback_data_manager.calculate_nps_score(days=30)
        top_issues = feedback_data_manager.get_top_issues(limit=5)
        trends = feedback_analytics.analyze_feedback_trends(days=7, granularity="daily")

        dashboard_data = {
            "overview": {
                "total_feedback": stats.get("basic_stats", {}).get("total_feedback", 0),
                "avg_rating": stats.get("basic_stats", {}).get("avg_rating", 0),
                "nps_score": nps_data.get("nps_score", 0),
                "unique_users": stats.get("basic_stats", {}).get("unique_users", 0)
            },
            "feedback_distribution": stats.get("feedback_types", {}),
            "satisfaction_metrics": {
                "avg_satisfaction": stats.get("satisfaction_stats", {}).get("avg_satisfaction", 0),
                "survey_count": stats.get("satisfaction_stats", {}).get("survey_count", 0),
                "nps_breakdown": {
                    "promoters": nps_data.get("promoters", 0),
                    "passives": nps_data.get("passives", 0),
                    "detractors": nps_data.get("detractors", 0)
                }
            },
            "top_issues": top_issues,
            "recent_trends": [trend.__dict__ for trend in trends[-7:]] if trends else []
        }

        return {
            "status": "success",
            "data": dashboard_data,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取反馈仪表板失败: {str(e)}")

@router.post("/auto-optimize")
async def trigger_auto_optimization():
    """触发自动优化"""
    try:
        result = await auto_optimization_engine.run_optimization_cycle()

        return {
            "status": "success",
            "message": "自动优化已触发",
            "data": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"触发自动优化失败: {str(e)}")

@router.delete("/batch")
async def delete_multiple_feedback(
    request_data: dict = Body(...)
):
    """批量删除反馈"""
    try:
        feedback_ids = request_data.get("feedback_ids", [])
        user_id = request_data.get("user_id")

        if not feedback_ids:
            raise HTTPException(status_code=400, detail="反馈ID列表不能为空")

        # 🆕 如果没有提供user_id或者是current_user，则不进行严格权限验证
        if user_id is None or user_id == 'current_user':
            result = feedback_data_manager.delete_multiple_feedback(feedback_ids, user_id=None)
        else:
            result = feedback_data_manager.delete_multiple_feedback(feedback_ids, user_id)

        # 🆕 批量删除总是返回成功，即使部分失败也返回详细结果
        return {
            "status": "success",
            "message": f"批量删除完成，成功删除 {result['deleted_count']}/{result['total_requested']} 条反馈",
            "data": result
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量删除反馈失败: {str(e)}")

@router.delete("/{feedback_id}")
async def delete_feedback(
    feedback_id: str,
    user_id: Optional[str] = Query(None, description="用户ID（用于权限验证）")
):
    """删除单条反馈"""
    try:
        # 🆕 如果没有提供user_id或者是current_user，则不进行严格权限验证
        # 这样可以删除任何反馈，适用于测试环境
        if user_id is None or user_id == 'current_user':
            success = feedback_data_manager.delete_feedback(feedback_id, user_id=None)
        else:
            success = feedback_data_manager.delete_feedback(feedback_id, user_id)

        if success:
            return {
                "status": "success",
                "message": "反馈删除成功",
                "data": {"feedback_id": feedback_id}
            }
        else:
            raise HTTPException(
                status_code=404,
                detail="反馈不存在或您没有权限删除此反馈"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除反馈失败: {str(e)}")

@router.post("/{feedback_id}/restore")
async def restore_feedback(
    feedback_id: str,
    request_data: dict = Body(...)
):
    """恢复已删除的反馈"""
    try:
        user_id = request_data.get("user_id")

        # 🆕 如果没有提供user_id或者是current_user，则不进行严格权限验证
        if user_id is None or user_id == 'current_user':
            success = feedback_data_manager.restore_feedback(feedback_id, user_id=None)
        else:
            success = feedback_data_manager.restore_feedback(feedback_id, user_id)

        if success:
            return {
                "status": "success",
                "message": "反馈恢复成功",
                "data": {"feedback_id": feedback_id}
            }
        else:
            raise HTTPException(
                status_code=404,
                detail="反馈不存在、未被删除或您没有权限恢复此反馈"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"恢复反馈失败: {str(e)}")

@router.get("/auto-optimize/status")
async def get_auto_optimization_status():
    """获取自动优化状态"""
    try:
        status = auto_optimization_engine.get_optimization_status()

        return {
            "status": "success",
            "data": status
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取自动优化状态失败: {str(e)}")

@router.post("/auto-optimize/analyze")
async def analyze_feedback_for_optimization():
    """分析反馈并生成优化建议"""
    try:
        # 🆕 获取用于分析的反馈数据（增加数量以确保分析全面性）
        analysis_feedback = feedback_data_manager.get_user_feedback(limit=50)

        # 🆕 获取用于显示的反馈数据（最近的反馈，包含完整信息）
        display_feedback = feedback_data_manager.get_user_feedback(limit=20)

        # 生成优化建议（传入分析用的反馈数据）
        actions = await auto_optimization_engine.analyze_feedback_and_optimize_with_data(analysis_feedback)

        # 🆕 增强反馈数据格式，确保包含所有必要字段
        formatted_feedback = []
        for feedback in display_feedback:
            formatted_item = {
                "feedback_id": feedback.get("feedback_id", ""),
                "content": feedback.get("content", ""),
                "feedback_type": feedback.get("feedback_type", "general"),
                "prediction_period": feedback.get("prediction_period", ""),
                "prediction_numbers": feedback.get("prediction_numbers", ""),
                "actual_numbers": feedback.get("actual_numbers", ""),
                "rating": feedback.get("rating", 0),
                "created_at": feedback.get("created_at", ""),
                # 🆕 添加额外的显示字段
                "tags": feedback.get("tags", []),
                "status": feedback.get("status", "pending"),
                "user_id": feedback.get("user_id", "anonymous")
            }
            formatted_feedback.append(formatted_item)

        return {
            "status": "success",
            "message": f"分析完成，基于 {len(analysis_feedback)} 条反馈生成了 {len(actions)} 个优化建议",
            "data": {
                "user_feedback": formatted_feedback,
                "optimization_actions": [
                    {
                        "action_id": action.action_id,
                        "action_type": action.action_type,
                        "target": action.target,
                        "description": action.description,
                        "priority": action.priority,
                        "impact_score": action.impact_score,
                        "auto_executable": action.auto_executable,
                        "parameters": action.parameters,
                        # 🆕 双层说明系统字段
                        "user_friendly_explanation": {
                            "problem_description": action.user_friendly_explanation.problem_description,
                            "optimization_reason": action.user_friendly_explanation.optimization_reason,
                            "method_explanation": action.user_friendly_explanation.method_explanation,
                            "expected_outcome": action.user_friendly_explanation.expected_outcome,
                            "user_action_required": action.user_friendly_explanation.user_action_required,
                            "difficulty_level": action.user_friendly_explanation.difficulty_level,
                            "estimated_time": action.user_friendly_explanation.estimated_time,
                            "risk_assessment": action.user_friendly_explanation.risk_assessment
                        } if action.user_friendly_explanation else None,
                        "ai_technical_spec": {
                            "optimization_category": action.ai_technical_spec.optimization_category,
                            "technical_approach": action.ai_technical_spec.technical_approach,
                            "implementation_details": action.ai_technical_spec.implementation_details,
                            "code_changes_required": action.ai_technical_spec.code_changes_required,
                            "dependencies": action.ai_technical_spec.dependencies,
                            "testing_requirements": action.ai_technical_spec.testing_requirements,
                            "rollback_strategy": action.ai_technical_spec.rollback_strategy,
                            "performance_impact": action.ai_technical_spec.performance_impact,
                            "monitoring_points": action.ai_technical_spec.monitoring_points,
                            "claude_prompt": action.ai_technical_spec.claude_prompt,
                            "execution_commands": action.ai_technical_spec.execution_commands
                        } if action.ai_technical_spec else None,
                        "explanation_generated": action.explanation_generated,
                        # 🆕 期号验证字段
                        "primary_source_period": action.primary_source_period,
                        "source_period_count": action.source_period_count,
                        "validated_feedback_count": action.validated_feedback_count,
                        "total_feedback_count": action.total_feedback_count,
                        "validation_rate": action.validation_rate,
                        "feedback_source_summary": action.feedback_source_summary,
                        "source_periods": action.source_periods
                    }
                    for action in actions
                ],
                "total_feedback": len(analysis_feedback),
                "total_actions": len(actions),
                "auto_executable_count": len([a for a in actions if a.auto_executable]),
                # 🆕 添加分析统计信息
                "analysis_stats": {
                    "display_feedback_count": len(display_feedback),
                    "analysis_feedback_count": len(analysis_feedback),
                    "feedback_types": _get_feedback_type_stats(analysis_feedback),
                    "avg_rating": _calculate_avg_rating(analysis_feedback)
                }
            }
        }
    except Exception as e:
        logger.error(f"分析反馈失败: {e}")
        raise HTTPException(status_code=500, detail=f"分析反馈失败: {str(e)}")

@router.get("/auto-optimize/scheduler/status")
async def get_scheduler_status():
    """获取自动优化调度器状态"""
    try:
        status = auto_optimization_scheduler.get_scheduler_status()

        return {
            "status": "success",
            "data": status
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取调度器状态失败: {str(e)}")

@router.post("/auto-optimize/scheduler/start")
async def start_scheduler():
    """启动自动优化调度器"""
    try:
        auto_optimization_scheduler.start()

        return {
            "status": "success",
            "message": "自动优化调度器已启动"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动调度器失败: {str(e)}")

@router.post("/auto-optimize/scheduler/stop")
async def stop_scheduler():
    """停止自动优化调度器"""
    try:
        auto_optimization_scheduler.stop()

        return {
            "status": "success",
            "message": "自动优化调度器已停止"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"停止调度器失败: {str(e)}")

@router.post("/auto-optimize/trigger")
async def trigger_immediate_optimization():
    """立即触发优化"""
    try:
        result = await auto_optimization_scheduler.trigger_immediate_optimization_async()

        return {
            "status": "success",
            "message": "立即优化已触发",
            "data": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"触发立即优化失败: {str(e)}")


# ==================== 🆕 数据管理专用API端点 ====================

class AdvancedQueryRequest(BaseModel):
    """高级查询请求模型"""
    # 基础查询参数
    user_id: Optional[str] = None
    feedback_type: Optional[str] = None
    status: Optional[str] = None

    # 时间范围查询
    start_date: Optional[str] = None  # ISO格式日期字符串
    end_date: Optional[str] = None

    # 内容查询
    content_keyword: Optional[str] = None
    prediction_period: Optional[str] = None

    # 评分范围查询
    min_rating: Optional[int] = None
    max_rating: Optional[int] = None

    # 分页和排序
    page: int = 1
    limit: int = 20
    sort_by: str = "created_at"  # created_at, rating, feedback_type
    order: str = "desc"  # asc, desc

    # 是否包含已删除数据
    include_deleted: bool = False


@router.post("/advanced-query")
async def advanced_query_feedback(request: AdvancedQueryRequest):
    """高级查询反馈数据"""
    try:
        # 构建查询条件
        query = "SELECT * FROM user_feedback WHERE 1=1"
        params = []

        # 状态过滤
        if not request.include_deleted:
            query += " AND status != 'deleted'"

        if request.status:
            query += " AND status = ?"
            params.append(request.status)

        # 用户ID过滤
        if request.user_id:
            query += " AND user_id = ?"
            params.append(request.user_id)

        # 反馈类型过滤
        if request.feedback_type:
            query += " AND feedback_type = ?"
            params.append(request.feedback_type)

        # 时间范围过滤
        if request.start_date:
            query += " AND created_at >= ?"
            params.append(request.start_date)

        if request.end_date:
            query += " AND created_at <= ?"
            params.append(request.end_date)

        # 内容关键词搜索
        if request.content_keyword:
            query += " AND content LIKE ?"
            params.append(f"%{request.content_keyword}%")

        # 期号过滤
        if request.prediction_period:
            query += " AND prediction_period = ?"
            params.append(request.prediction_period)

        # 评分范围过滤
        if request.min_rating is not None:
            query += " AND rating >= ?"
            params.append(request.min_rating)

        if request.max_rating is not None:
            query += " AND rating <= ?"
            params.append(request.max_rating)

        # 排序
        valid_sort_fields = ["created_at", "rating", "feedback_type", "user_id", "updated_at"]
        sort_field = request.sort_by if request.sort_by in valid_sort_fields else "created_at"
        sort_order = "ASC" if request.order.lower() == "asc" else "DESC"
        query += f" ORDER BY {sort_field} {sort_order}"

        # 分页
        offset = (request.page - 1) * request.limit
        query += " LIMIT ? OFFSET ?"
        params.extend([request.limit, offset])

        # 执行查询
        import sqlite3
        with sqlite3.connect(feedback_data_manager.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute(query, params)

            results = []
            for row in cursor.fetchall():
                feedback_dict = dict(row)
                # 解析JSON字段
                if feedback_dict.get('tags'):
                    try:
                        feedback_dict['tags'] = json.loads(feedback_dict['tags'])
                    except:
                        feedback_dict['tags'] = []
                if feedback_dict.get('metadata'):
                    try:
                        feedback_dict['metadata'] = json.loads(feedback_dict['metadata'])
                    except:
                        feedback_dict['metadata'] = {}
                results.append(feedback_dict)

            # 获取总数（用于分页）
            count_query = query.split("ORDER BY")[0].replace("SELECT *", "SELECT COUNT(*)")
            count_params = params[:-2]  # 移除LIMIT和OFFSET参数
            cursor = conn.execute(count_query, count_params)
            total_count = cursor.fetchone()[0]

        return {
            "status": "success",
            "data": {
                "items": results,
                "pagination": {
                    "page": request.page,
                    "limit": request.limit,
                    "total": total_count,
                    "pages": (total_count + request.limit - 1) // request.limit
                },
                "query_info": {
                    "sort_by": sort_field,
                    "order": sort_order,
                    "filters_applied": len([p for p in [request.user_id, request.feedback_type, request.content_keyword] if p])
                }
            }
        }

    except Exception as e:
        logger.error(f"高级查询失败: {e}")
        raise HTTPException(status_code=500, detail=f"高级查询失败: {str(e)}")


@router.get("/data-statistics")
async def get_data_statistics(
    days: int = Query(30, description="统计天数"),
    include_deleted: bool = Query(False, description="是否包含已删除数据")
):
    """获取数据库统计信息（专用于数据管理）"""
    try:
        from datetime import datetime, timedelta

        start_date = datetime.now() - timedelta(days=days)

        with sqlite3.connect(feedback_data_manager.db_path) as conn:
            conn.row_factory = sqlite3.Row

            # 基础统计
            status_filter = "" if include_deleted else "AND status != 'deleted'"

            # 总数据量统计
            cursor = conn.execute(f"""
                SELECT
                    COUNT(*) as total_feedback,
                    COUNT(DISTINCT user_id) as unique_users,
                    AVG(rating) as avg_rating,
                    MIN(created_at) as earliest_date,
                    MAX(created_at) as latest_date
                FROM user_feedback
                WHERE created_at >= ? {status_filter}
            """, (start_date,))
            basic_stats = dict(cursor.fetchone())

            # 反馈类型分布
            cursor = conn.execute(f"""
                SELECT feedback_type, COUNT(*) as count
                FROM user_feedback
                WHERE created_at >= ? {status_filter}
                GROUP BY feedback_type
                ORDER BY count DESC
            """, (start_date,))
            feedback_types = [{"type": row['feedback_type'], "count": row['count']} for row in cursor.fetchall()]

            # 状态分布
            cursor = conn.execute("""
                SELECT status, COUNT(*) as count
                FROM user_feedback
                WHERE created_at >= ?
                GROUP BY status
                ORDER BY count DESC
            """, (start_date,))
            status_distribution = [{"status": row['status'], "count": row['count']} for row in cursor.fetchall()]

            # 用户活跃度统计（按用户反馈数量分组）
            cursor = conn.execute(f"""
                SELECT
                    user_id,
                    COUNT(*) as feedback_count,
                    AVG(rating) as avg_rating,
                    MAX(created_at) as last_feedback
                FROM user_feedback
                WHERE created_at >= ? {status_filter}
                GROUP BY user_id
                ORDER BY feedback_count DESC
                LIMIT 20
            """, (start_date,))
            top_users = [dict(row) for row in cursor.fetchall()]

            # 时间趋势（按天统计）
            cursor = conn.execute(f"""
                SELECT
                    DATE(created_at) as date,
                    COUNT(*) as count,
                    AVG(rating) as avg_rating
                FROM user_feedback
                WHERE created_at >= ? {status_filter}
                GROUP BY DATE(created_at)
                ORDER BY date DESC
                LIMIT 30
            """, (start_date,))
            daily_trends = [dict(row) for row in cursor.fetchall()]

            # 评分分布
            cursor = conn.execute(f"""
                SELECT
                    rating,
                    COUNT(*) as count
                FROM user_feedback
                WHERE created_at >= ? AND rating IS NOT NULL {status_filter}
                GROUP BY rating
                ORDER BY rating
            """, (start_date,))
            rating_distribution = [{"rating": row['rating'], "count": row['count']} for row in cursor.fetchall()]

            # 期号统计（最活跃的期号）
            cursor = conn.execute(f"""
                SELECT
                    prediction_period,
                    COUNT(*) as count,
                    AVG(rating) as avg_rating
                FROM user_feedback
                WHERE created_at >= ? AND prediction_period IS NOT NULL {status_filter}
                GROUP BY prediction_period
                ORDER BY count DESC
                LIMIT 10
            """, (start_date,))
            period_stats = [dict(row) for row in cursor.fetchall()]

        return {
            "status": "success",
            "data": {
                "basic_stats": basic_stats,
                "distributions": {
                    "feedback_types": feedback_types,
                    "status": status_distribution,
                    "ratings": rating_distribution
                },
                "user_activity": {
                    "top_users": top_users,
                    "user_segments": _analyze_user_segments(top_users)
                },
                "time_trends": {
                    "daily": daily_trends,
                    "period_stats": period_stats
                },
                "data_quality": {
                    "completion_rate": _calculate_completion_rate(basic_stats),
                    "avg_content_length": _calculate_avg_content_length(start_date, include_deleted),
                    "test_data_indicators": _detect_test_data_patterns(start_date, include_deleted)
                }
            },
            "query_info": {
                "days": days,
                "include_deleted": include_deleted,
                "generated_at": datetime.now().isoformat()
            }
        }

    except Exception as e:
        logger.error(f"获取数据统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据统计失败: {str(e)}")


class TestDataDetectionRequest(BaseModel):
    """测试数据检测请求模型"""
    feedback_ids: Optional[List[str]] = None  # 指定要检测的反馈ID列表
    limit: int = 100  # 检测数量限制
    threshold: float = 0.7  # 测试数据概率阈值


@router.post("/detect-test-data")
async def detect_test_data(request: TestDataDetectionRequest):
    """检测疑似测试数据"""
    try:
        # 构建查询条件
        if request.feedback_ids:
            # 检测指定的反馈
            placeholders = ','.join(['?' for _ in request.feedback_ids])
            query = f"SELECT * FROM user_feedback WHERE feedback_id IN ({placeholders})"
            params = request.feedback_ids
        else:
            # 检测最近的反馈
            query = "SELECT * FROM user_feedback ORDER BY created_at DESC LIMIT ?"
            params = [request.limit]

        with sqlite3.connect(feedback_data_manager.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute(query, params)

            results = []
            for row in cursor.fetchall():
                feedback_dict = dict(row)

                # 计算测试数据概率
                test_score = _calculate_test_data_score(feedback_dict)

                # 生成检测原因
                reasons = _generate_test_data_reasons(feedback_dict, test_score)

                # 确定风险等级
                risk_level = _determine_risk_level(test_score)

                result = {
                    "feedback_id": feedback_dict["feedback_id"],
                    "user_id": feedback_dict["user_id"],
                    "content": feedback_dict["content"][:100] + "..." if len(feedback_dict["content"]) > 100 else feedback_dict["content"],
                    "feedback_type": feedback_dict["feedback_type"],
                    "created_at": feedback_dict["created_at"],
                    "test_probability": round(test_score, 3),
                    "risk_level": risk_level,
                    "reasons": reasons,
                    "is_likely_test": test_score >= request.threshold
                }
                results.append(result)

            # 按测试概率排序
            results.sort(key=lambda x: x["test_probability"], reverse=True)

            # 统计信息
            total_checked = len(results)
            likely_test = len([r for r in results if r["is_likely_test"]])

            return {
                "status": "success",
                "data": {
                    "detections": results,
                    "summary": {
                        "total_checked": total_checked,
                        "likely_test_count": likely_test,
                        "test_percentage": round((likely_test / total_checked * 100) if total_checked > 0 else 0, 2),
                        "threshold_used": request.threshold
                    },
                    "recommendations": _generate_cleanup_recommendations(results, request.threshold)
                }
            }

    except Exception as e:
        logger.error(f"测试数据检测失败: {e}")
        raise HTTPException(status_code=500, detail=f"测试数据检测失败: {str(e)}")


# 🆕 辅助函数
def _get_feedback_type_stats(feedback_list):
    """获取反馈类型统计"""
    try:
        type_counts = {}
        for feedback in feedback_list:
            feedback_type = feedback.get("feedback_type", "general")
            type_counts[feedback_type] = type_counts.get(feedback_type, 0) + 1
        return type_counts
    except Exception:
        return {}


def _calculate_avg_rating(feedback_list):
    """计算平均评分"""
    try:
        ratings = [feedback.get("rating", 0) for feedback in feedback_list if feedback.get("rating")]
        return sum(ratings) / len(ratings) if ratings else 0.0
    except Exception:
        return 0.0


def _analyze_user_segments(top_users):
    """分析用户活跃度分段"""
    try:
        if not top_users:
            return {"high": 0, "medium": 0, "low": 0}

        feedback_counts = [user['feedback_count'] for user in top_users]
        if not feedback_counts:
            return {"high": 0, "medium": 0, "low": 0}

        max_count = max(feedback_counts)
        high_threshold = max_count * 0.7
        medium_threshold = max_count * 0.3

        segments = {"high": 0, "medium": 0, "low": 0}
        for count in feedback_counts:
            if count >= high_threshold:
                segments["high"] += 1
            elif count >= medium_threshold:
                segments["medium"] += 1
            else:
                segments["low"] += 1

        return segments
    except Exception:
        return {"high": 0, "medium": 0, "low": 0}


def _calculate_completion_rate(basic_stats):
    """计算数据完整性评分"""
    try:
        total = basic_stats.get('total_feedback', 0)
        if total == 0:
            return 0.0

        # 简化的完整性计算，实际项目中可以更复杂
        # 这里假设有评分的反馈更完整
        return min(100.0, (basic_stats.get('avg_rating', 0) / 5.0) * 100)
    except Exception:
        return 0.0


def _calculate_avg_content_length(start_date, include_deleted):
    """计算平均内容长度"""
    try:
        status_filter = "" if include_deleted else "AND status != 'deleted'"

        with sqlite3.connect(feedback_data_manager.db_path) as conn:
            cursor = conn.execute(f"""
                SELECT AVG(LENGTH(content)) as avg_length
                FROM user_feedback
                WHERE created_at >= ? {status_filter}
            """, (start_date,))

            result = cursor.fetchone()
            return round(result[0] if result[0] else 0.0, 2)
    except Exception:
        return 0.0


def _detect_test_data_patterns(start_date, include_deleted):
    """检测测试数据模式"""
    try:
        status_filter = "" if include_deleted else "AND status != 'deleted'"

        with sqlite3.connect(feedback_data_manager.db_path) as conn:
            # 检测重复内容
            cursor = conn.execute(f"""
                SELECT content, COUNT(*) as count
                FROM user_feedback
                WHERE created_at >= ? {status_filter}
                GROUP BY content
                HAVING count > 1
                ORDER BY count DESC
                LIMIT 5
            """, (start_date,))
            duplicate_content = [{"content": row[0][:50] + "...", "count": row[1]} for row in cursor.fetchall()]

            # 检测测试用户模式
            cursor = conn.execute(f"""
                SELECT user_id, COUNT(*) as count
                FROM user_feedback
                WHERE created_at >= ? {status_filter}
                AND (user_id LIKE '%test%' OR user_id LIKE '%demo%' OR user_id = 'anonymous')
                GROUP BY user_id
                ORDER BY count DESC
            """, (start_date,))
            test_users = [{"user_id": row[0], "count": row[1]} for row in cursor.fetchall()]

            # 检测默认内容模式
            cursor = conn.execute(f"""
                SELECT COUNT(*) as count
                FROM user_feedback
                WHERE created_at >= ? {status_filter}
                AND (content LIKE '%请在此补充%' OR content LIKE '%测试%' OR content LIKE '%test%')
            """, (start_date,))
            default_content_count = cursor.fetchone()[0]

            return {
                "duplicate_content": duplicate_content,
                "test_users": test_users,
                "default_content_count": default_content_count,
                "risk_score": min(100, len(duplicate_content) * 10 + len(test_users) * 15 + default_content_count * 5)
            }
    except Exception:
        return {
            "duplicate_content": [],
            "test_users": [],
            "default_content_count": 0,
            "risk_score": 0
        }


def _calculate_test_data_score(feedback_dict):
    """计算测试数据概率评分（0-1之间）"""
    try:
        score = 0.0

        # 用户ID检查
        user_id = feedback_dict.get("user_id", "").lower()
        if any(keyword in user_id for keyword in ["test", "demo", "admin", "anonymous"]):
            score += 0.3

        # 内容检查
        content = feedback_dict.get("content", "").lower()
        test_keywords = ["测试", "test", "请在此补充", "默认", "示例", "example", "demo"]
        for keyword in test_keywords:
            if keyword in content:
                score += 0.2
                break

        # 内容长度检查（过短或过长可能是测试）
        content_length = len(feedback_dict.get("content", ""))
        if content_length < 10:
            score += 0.15
        elif content_length > 500:
            score += 0.1

        # 评分检查（没有评分或极端评分）
        rating = feedback_dict.get("rating")
        if rating is None:
            score += 0.1
        elif rating in [1, 5]:  # 极端评分可能是测试
            score += 0.05

        # 预测号码检查（重复的测试号码）
        prediction_numbers = feedback_dict.get("prediction_numbers", "")
        test_numbers = ["123", "456", "789", "000", "111", "222", "333", "444", "555", "666", "777", "888", "999"]
        if prediction_numbers in test_numbers:
            score += 0.2

        # 时间模式检查（短时间内大量提交）
        # 这里简化处理，实际可以查询同一用户短时间内的提交数量

        return min(1.0, score)  # 确保不超过1.0
    except Exception:
        return 0.0


def _generate_test_data_reasons(feedback_dict, test_score):
    """生成测试数据检测原因"""
    try:
        reasons = []

        user_id = feedback_dict.get("user_id", "").lower()
        content = feedback_dict.get("content", "").lower()

        if any(keyword in user_id for keyword in ["test", "demo", "admin", "anonymous"]):
            reasons.append("用户ID包含测试关键词")

        if any(keyword in content for keyword in ["测试", "test", "请在此补充", "默认", "示例"]):
            reasons.append("内容包含测试或默认文本")

        content_length = len(feedback_dict.get("content", ""))
        if content_length < 10:
            reasons.append("内容过短，可能是测试数据")
        elif content_length > 500:
            reasons.append("内容异常长，可能是测试数据")

        if feedback_dict.get("rating") is None:
            reasons.append("缺少评分信息")

        prediction_numbers = feedback_dict.get("prediction_numbers", "")
        if prediction_numbers in ["123", "456", "789", "000", "111", "222", "333", "444", "555", "666", "777", "888", "999"]:
            reasons.append("使用常见测试号码")

        if not reasons:
            reasons.append("数据质量良好")

        return reasons
    except Exception:
        return ["检测过程出错"]


def _determine_risk_level(test_score):
    """确定风险等级"""
    if test_score >= 0.8:
        return "high"
    elif test_score >= 0.5:
        return "medium"
    elif test_score >= 0.3:
        return "low"
    else:
        return "safe"


def _generate_cleanup_recommendations(results, threshold):
    """生成清理建议"""
    try:
        high_risk = [r for r in results if r["test_probability"] >= 0.8]
        medium_risk = [r for r in results if 0.5 <= r["test_probability"] < 0.8]

        recommendations = []

        if high_risk:
            recommendations.append({
                "priority": "high",
                "action": "立即删除",
                "count": len(high_risk),
                "description": f"建议立即删除 {len(high_risk)} 条高风险测试数据"
            })

        if medium_risk:
            recommendations.append({
                "priority": "medium",
                "action": "人工审核",
                "count": len(medium_risk),
                "description": f"建议人工审核 {len(medium_risk)} 条中风险数据"
            })

        # 用户清理建议
        test_users = set()
        for result in results:
            if result["test_probability"] >= threshold:
                test_users.add(result["user_id"])

        if test_users:
            recommendations.append({
                "priority": "medium",
                "action": "用户清理",
                "count": len(test_users),
                "description": f"发现 {len(test_users)} 个疑似测试用户，建议清理其所有数据"
            })

        return recommendations
    except Exception:
        return []


class BatchOperationRequest(BaseModel):
    """批量操作请求模型"""
    operation_type: str  # delete, update_status, tag

    # 操作目标（二选一）
    feedback_ids: Optional[List[str]] = None  # 指定ID列表
    query_conditions: Optional[AdvancedQueryRequest] = None  # 按条件操作

    # 操作参数
    new_status: Optional[str] = None  # 用于update_status操作
    tags_to_add: Optional[List[str]] = None  # 用于tag操作

    # 安全确认
    confirm_operation: bool = False  # 必须确认才能执行
    operator_id: str = "system"  # 操作员ID


@router.post("/batch-operations")
async def batch_operations(request: BatchOperationRequest):
    """增强的批量操作API"""
    try:
        if not request.confirm_operation:
            raise HTTPException(status_code=400, detail="必须确认操作才能执行")

        # 获取目标反馈ID列表
        target_ids = []
        if request.feedback_ids:
            target_ids = request.feedback_ids
        elif request.query_conditions:
            # 根据查询条件获取ID列表
            query_result = await advanced_query_feedback(request.query_conditions)
            target_ids = [item["feedback_id"] for item in query_result["data"]["items"]]
        else:
            raise HTTPException(status_code=400, detail="必须指定操作目标（ID列表或查询条件）")

        if not target_ids:
            return {
                "status": "success",
                "message": "没有找到符合条件的数据",
                "data": {
                    "operation_type": request.operation_type,
                    "affected_count": 0,
                    "results": []
                }
            }

        # 记录操作日志
        operation_log = {
            "operation_id": f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "operation_type": request.operation_type,
            "operator_id": request.operator_id,
            "target_count": len(target_ids),
            "timestamp": datetime.now().isoformat(),
            "parameters": {
                "new_status": request.new_status,
                "tags_to_add": request.tags_to_add
            }
        }

        results = []
        success_count = 0

        # 执行批量操作
        if request.operation_type == "delete":
            # 批量删除
            delete_result = feedback_data_manager.delete_multiple_feedback(target_ids, user_id=None)
            success_count = delete_result.get("deleted_count", 0)
            results = delete_result.get("results", [])

        elif request.operation_type == "update_status":
            # 批量更新状态
            if not request.new_status:
                raise HTTPException(status_code=400, detail="更新状态操作需要指定new_status")

            with sqlite3.connect(feedback_data_manager.db_path) as conn:
                for feedback_id in target_ids:
                    try:
                        cursor = conn.execute(
                            "UPDATE user_feedback SET status = ?, updated_at = ? WHERE feedback_id = ?",
                            (request.new_status, datetime.now().isoformat(), feedback_id)
                        )
                        if cursor.rowcount > 0:
                            success_count += 1
                            results.append({"feedback_id": feedback_id, "status": "success"})
                        else:
                            results.append({"feedback_id": feedback_id, "status": "not_found"})
                    except Exception as e:
                        results.append({"feedback_id": feedback_id, "status": "error", "error": str(e)})
                conn.commit()

        elif request.operation_type == "tag":
            # 批量添加标签
            if not request.tags_to_add:
                raise HTTPException(status_code=400, detail="标签操作需要指定tags_to_add")

            with sqlite3.connect(feedback_data_manager.db_path) as conn:
                for feedback_id in target_ids:
                    try:
                        # 获取现有标签
                        cursor = conn.execute("SELECT tags FROM user_feedback WHERE feedback_id = ?", (feedback_id,))
                        row = cursor.fetchone()
                        if row:
                            existing_tags = json.loads(row[0]) if row[0] else []
                            # 合并标签
                            new_tags = list(set(existing_tags + request.tags_to_add))
                            # 更新标签
                            cursor = conn.execute(
                                "UPDATE user_feedback SET tags = ?, updated_at = ? WHERE feedback_id = ?",
                                (json.dumps(new_tags), datetime.now().isoformat(), feedback_id)
                            )
                            success_count += 1
                            results.append({"feedback_id": feedback_id, "status": "success", "new_tags": new_tags})
                        else:
                            results.append({"feedback_id": feedback_id, "status": "not_found"})
                    except Exception as e:
                        results.append({"feedback_id": feedback_id, "status": "error", "error": str(e)})
                conn.commit()
        else:
            raise HTTPException(status_code=400, detail=f"不支持的操作类型: {request.operation_type}")

        # 保存操作日志
        operation_log["success_count"] = success_count
        operation_log["results"] = results
        _save_operation_log(operation_log)

        return {
            "status": "success",
            "message": f"批量{request.operation_type}操作完成，成功处理 {success_count}/{len(target_ids)} 条记录",
            "data": {
                "operation_id": operation_log["operation_id"],
                "operation_type": request.operation_type,
                "affected_count": success_count,
                "total_requested": len(target_ids),
                "results": results,
                "operation_log": operation_log
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量操作失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量操作失败: {str(e)}")


@router.get("/operation-logs")
async def get_operation_logs(
    limit: int = Query(20, description="返回数量限制"),
    operation_type: Optional[str] = Query(None, description="操作类型过滤")
):
    """获取操作日志"""
    try:
        logs = _get_operation_logs(limit, operation_type)

        return {
            "status": "success",
            "data": logs,
            "count": len(logs)
        }
    except Exception as e:
        logger.error(f"获取操作日志失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取操作日志失败: {str(e)}")


def _save_operation_log(operation_log):
    """保存操作日志"""
    try:
        # 简化实现：保存到文件
        import os
        log_dir = "logs/operations"
        os.makedirs(log_dir, exist_ok=True)

        log_file = os.path.join(log_dir, f"{operation_log['operation_id']}.json")
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(operation_log, f, ensure_ascii=False, indent=2)

        logger.info(f"操作日志已保存: {log_file}")
    except Exception as e:
        logger.error(f"保存操作日志失败: {e}")


def _get_operation_logs(limit, operation_type=None):
    """获取操作日志"""
    try:
        import os
        import glob

        log_dir = "logs/operations"
        if not os.path.exists(log_dir):
            return []

        log_files = glob.glob(os.path.join(log_dir, "*.json"))
        log_files.sort(key=os.path.getmtime, reverse=True)  # 按修改时间倒序

        logs = []
        for log_file in log_files[:limit]:
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    log_data = json.load(f)
                    if operation_type is None or log_data.get("operation_type") == operation_type:
                        logs.append(log_data)
            except Exception as e:
                logger.error(f"读取日志文件失败 {log_file}: {e}")

        return logs
    except Exception as e:
        logger.error(f"获取操作日志失败: {e}")
        return []

@router.post("/optimization/generate-explanation")
async def generate_optimization_explanation(action_data: dict):
    """为优化建议生成双层说明"""
    try:
        from ...core.optimization_explainer import optimization_explainer
        from ...core.auto_optimization_engine import OptimizationAction
        from datetime import datetime

        # 创建OptimizationAction对象
        action = OptimizationAction(
            action_id=action_data.get("action_id", f"manual_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
            action_type=action_data.get("action_type", "general"),
            target=action_data.get("target", "system"),
            description=action_data.get("description", ""),
            priority=action_data.get("priority", 3),
            impact_score=action_data.get("impact_score", 0.5),
            implementation_effort=action_data.get("implementation_effort", "medium"),
            auto_executable=action_data.get("auto_executable", False),
            parameters=action_data.get("parameters", {}),
            created_at=datetime.now()
        )

        # 生成双层说明
        user_explanation, ai_spec = optimization_explainer.generate_dual_explanation(action)

        return {
            "status": "success",
            "data": {
                "user_friendly_explanation": {
                    "problem_description": user_explanation.problem_description,
                    "optimization_reason": user_explanation.optimization_reason,
                    "method_explanation": user_explanation.method_explanation,
                    "expected_outcome": user_explanation.expected_outcome,
                    "user_action_required": user_explanation.user_action_required,
                    "estimated_improvement": user_explanation.estimated_improvement,
                    "risk_assessment": user_explanation.risk_assessment,
                    "completion_time": user_explanation.completion_time
                },
                "ai_technical_spec": {
                    "optimization_category": ai_spec.optimization_category,
                    "target_components": ai_spec.target_components,
                    "technical_parameters": ai_spec.technical_parameters,
                    "execution_instructions": ai_spec.execution_instructions,
                    "rollback_strategy": ai_spec.rollback_strategy,
                    "success_criteria": ai_spec.success_criteria,
                    "monitoring_metrics": ai_spec.monitoring_metrics,
                    "claude_prompt": ai_spec.claude_prompt,
                    "execution_commands": ai_spec.execution_commands
                }
            }
        }

    except Exception as e:
        logger.error(f"生成优化说明失败: {e}")
        raise HTTPException(status_code=500, detail=f"生成优化说明失败: {str(e)}")

@router.get("/optimization/explanation-templates")
async def get_explanation_templates():
    """获取说明模板信息"""
    try:
        templates = {
            "optimization_types": [
                {
                    "type": "algorithm_improve",
                    "name": "算法改进",
                    "description": "优化预测算法参数和模型配置"
                },
                {
                    "type": "parameter_adjust",
                    "name": "参数调整",
                    "description": "调整系统参数以提升性能"
                },
                {
                    "type": "feature_toggle",
                    "name": "功能切换",
                    "description": "启用或禁用特定功能"
                },
                {
                    "type": "ui_optimize",
                    "name": "界面优化",
                    "description": "改进用户界面和交互体验"
                }
            ],
            "effort_levels": ["low", "medium", "high"],
            "priority_range": [1, 2, 3, 4, 5],
            "impact_score_range": [0.0, 1.0]
        }

        return {
            "status": "success",
            "data": templates
        }

    except Exception as e:
        logger.error(f"获取说明模板失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取说明模板失败: {str(e)}")


class ExportRequest(BaseModel):
    """技术规格导出请求模型"""
    action_ids: Optional[List[str]] = None  # 指定要导出的优化建议ID列表，为空则导出所有
    format: str = "markdown"  # 导出格式：markdown 或 json
    include_metadata: bool = True  # 是否包含元数据
    period_filter: Optional[str] = None  # 期号过滤


@router.post("/export-technical-specs")
async def export_technical_specs(request: ExportRequest):
    """
    导出优化建议的技术规格

    支持批量导出和格式选择，包含服务端数据处理和优化
    """
    try:
        logger.info(f"开始导出技术规格，格式: {request.format}, 包含元数据: {request.include_metadata}")

        # 获取优化建议数据
        if request.action_ids:
            # 导出指定的优化建议
            actions_data = []
            for action_id in request.action_ids:
                try:
                    # 这里应该从数据库或缓存中获取具体的优化建议数据
                    # 由于当前系统主要在前端处理，这里提供一个基础实现
                    action_data = {
                        "action_id": action_id,
                        "action_type": "system_optimization",
                        "description": f"优化建议 {action_id}",
                        "priority": 3,
                        "impact_score": 0.7,
                        "auto_executable": True,
                        "ai_technical_spec": {
                            "optimization_category": "system_optimization",
                            "code_changes_required": ["自动执行优化", "参数调整", "性能监控"],
                            "execution_strategy": "基于反馈分析的自动优化",
                            "success_criteria": ["系统性能提升", "用户满意度改善"],
                            "monitoring_metrics": ["响应时间", "错误率", "用户反馈评分"],
                            "claude_prompts": [f"优化系统组件 {action_id}"],
                            "execution_commands": ["python src/optimization/auto_optimizer.py"],
                            "estimated_time": "30分钟",
                            "risk_assessment": "低风险"
                        }
                    }
                    actions_data.append(action_data)
                except Exception as e:
                    logger.warning(f"获取优化建议 {action_id} 失败: {e}")
                    continue
        else:
            # 导出所有可用的优化建议
            # 这里可以从自动优化引擎获取当前的优化建议
            try:
                # 尝试获取当前的优化建议
                analysis_result = auto_optimization_engine.analyze_feedback()
                actions_data = analysis_result.get('optimization_actions', [])
            except Exception as e:
                logger.warning(f"获取自动优化建议失败: {e}")
                actions_data = []

        if not actions_data:
            return {
                "status": "success",
                "message": "暂无可导出的技术规格",
                "data": {
                    "export_info": {
                        "total_actions": 0,
                        "export_time": datetime.now().isoformat(),
                        "format": request.format
                    },
                    "content": ""
                }
            }

        # 准备导出数据
        export_info = {
            "period": request.period_filter or "当前期号",
            "total_actions": len(actions_data),
            "validation_rate": 100.0,  # 服务端数据默认100%验证
            "export_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "data_source": "服务端自动优化引擎"
        }

        export_data = {
            "export_info": export_info,
            "optimization_actions": actions_data
        }

        # 根据格式生成内容
        if request.format.lower() == "json":
            # JSON格式导出
            content = {
                "export_metadata": {
                    "format_version": "1.0",
                    "generator": "福彩3D智能预测系统-服务端",
                    "export_purpose": "Augment系统升级",
                    **export_info
                },
                "technical_specifications": actions_data,
                "implementation_recommendations": {
                    "priority_order": sorted(actions_data, key=lambda x: x.get('priority', 0), reverse=True)[:3],
                    "auto_executable_count": len([a for a in actions_data if a.get('auto_executable', False)]),
                    "manual_review_count": len([a for a in actions_data if not a.get('auto_executable', True)])
                }
            }

            return {
                "status": "success",
                "data": {
                    "export_info": export_info,
                    "content": content,
                    "filename": f"fucai3d_technical_specs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    "content_type": "application/json"
                }
            }

        else:
            # Markdown格式导出
            markdown_content = f"""# 🎯 福彩3D系统优化技术规格

## 📊 基础信息
- **期号**: {export_info['period']}期
- **优化建议数量**: {export_info['total_actions']}个
- **数据验证率**: {export_info['validation_rate']}%
- **数据源**: {export_info['data_source']}
- **导出时间**: {export_info['export_time']}

## 🚀 系统升级建议

基于服务端自动优化引擎分析，系统识别出以下优化机会。这些技术规格可直接用于Augment系统升级和主动优化迭代。

## 📋 优化建议详情

"""

            for i, action in enumerate(actions_data, 1):
                spec = action.get('ai_technical_spec', {})
                markdown_content += f"""### {i}. {action.get('description', '未知优化建议')}

#### 🔧 技术参数
- **优化类别**: {spec.get('optimization_category', '未知')}
- **代码修改要求**: {', '.join(spec.get('code_changes_required', []))}
- **风险评估**: {spec.get('risk_assessment', '未评估')}
- **预计用时**: {spec.get('estimated_time', '未知')}

#### ⚡ 执行策略
```
{spec.get('execution_strategy', '未定义')}
```

#### ✅ 成功标准与监控
{chr(10).join([f'- {criteria}' for criteria in spec.get('success_criteria', [])])}

**监控指标**:
{chr(10).join([f'- {metric}' for metric in spec.get('monitoring_metrics', [])])}

#### 🤖 Claude 4 提示
```
{chr(10).join(spec.get('claude_prompts', []))}
```

#### 💻 执行命令
```bash
{chr(10).join(spec.get('execution_commands', []))}
```

---

"""

            markdown_content += f"""## 🎯 实施建议

### 优先级排序
{chr(10).join([f'{i+1}. {action.get("description", "未知")} (优先级: {action.get("priority", 0)})' for i, action in enumerate(sorted(actions_data, key=lambda x: x.get('priority', 0), reverse=True)[:3])])}

### 自动执行建议
{chr(10).join([f'- {action.get("description", "未知")}' for action in actions_data if action.get('auto_executable', False)])}

### 风险控制
- 建议分批执行，优先处理低风险项目
- 每次执行后进行充分测试验证
- 保持完整的回滚机制

---
*本技术规格由福彩3D智能预测系统服务端自动生成，基于自动优化引擎分析*
"""

            return {
                "status": "success",
                "data": {
                    "export_info": export_info,
                    "content": markdown_content,
                    "filename": f"fucai3d_technical_specs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
                    "content_type": "text/markdown"
                }
            }

    except Exception as e:
        logger.error(f"导出技术规格失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出技术规格失败: {str(e)}")


@router.get("/export-technical-specs/status")
async def get_export_status():
    """
    获取导出功能状态
    """
    try:
        # 检查自动优化引擎状态
        try:
            engine_status = auto_optimization_engine.get_status()
            available_actions = len(engine_status.get('pending_actions', []))
        except Exception:
            available_actions = 0

        return {
            "status": "success",
            "data": {
                "export_available": True,
                "available_actions": available_actions,
                "supported_formats": ["markdown", "json"],
                "last_export_time": None,  # 可以从数据库获取
                "export_count_today": 0    # 可以从日志统计
            }
        }

    except Exception as e:
        logger.error(f"获取导出状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取导出状态失败: {str(e)}")
