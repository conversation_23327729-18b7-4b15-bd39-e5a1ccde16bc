import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Row,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Switch,
  Timeline,
  Progress,
  Tag,
  Space,
  Typography,
  Divider,
  Alert,
  List,
  Badge,
  Tooltip,
  Modal,
  Tabs,
  message,
  Spin
} from 'antd';
import {
  RobotOutlined,
  <PERSON>boltOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  BulbOutlined,
  TrophyOutlined,
  FireOutlined,
  EyeOutlined,

  FileMarkdownOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import axios from 'axios';
import OptimizationExplanation from './OptimizationExplanation';
import {
  formatTechnicalSpecToMarkdown,
  formatTechnicalSpecToJSON,
  downloadFile,

  prepareExportData,
  type OptimizationAction as ExportOptimizationAction
} from '../utils/exportTemplates';

const { Title, Text, Paragraph } = Typography;

interface OptimizationStatus {
  engine_status: string;
  total_executions: number;
  recent_executions: Array<{
    action_id: string;
    execution_time: string;
    success: boolean;
    notes: string;
  }>;
  optimization_rules_count: number;
  last_check: string;
  // 双引擎状态
  dual_engine_status?: {
    quick_optimization: {
      status: string;
      last_execution: string;
      success_rate: number;
    };
    deep_optimization: {
      status: string;
      last_execution: string;
      shap_analysis_count: number;
    };
    feedback_parser: {
      total_parsed: number;
      auto_actionable_rate: number;
      last_parsed_feedback: string;
    };
  };
}

interface SchedulerStatus {
  is_running: boolean;
  optimization_interval: number;
  last_optimization_time: string | null;
  optimization_count: number;
  next_optimization_time: string;
  auto_optimization_enabled: boolean;
}

interface OptimizationAction {
  action_id: string;
  action_type: string;
  target: string;
  description: string;
  priority: number;
  impact_score: number;
  auto_executable: boolean;
  parameters: any;
}

const AutoOptimizationDashboard: React.FC = () => {
  const [optimizationStatus, setOptimizationStatus] = useState<OptimizationStatus | null>(null);
  const [schedulerStatus, setSchedulerStatus] = useState<SchedulerStatus | null>(null);
  const [pendingActions, setPendingActions] = useState<OptimizationAction[]>([]);
  const [userFeedback, setUserFeedback] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [autoOptEnabled, setAutoOptEnabled] = useState(true);
  const [showAnalysisModal, setShowAnalysisModal] = useState(false);
  const [explanationViewMode, setExplanationViewMode] = useState<'user' | 'expert' | 'both'>('user');
  // 🆕 批量导出状态管理
  const [batchExportLoading, setBatchExportLoading] = useState({
    markdown: false,
    json: false
  });

  useEffect(() => {
    loadOptimizationData();
    // 每30秒刷新一次数据
    const interval = setInterval(loadOptimizationData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadOptimizationData = async () => {
    try {
      const [statusRes, schedulerRes] = await Promise.all([
        axios.get('/api/feedback/auto-optimize/status'),
        axios.get('/api/feedback/auto-optimize/scheduler/status')
      ]);

      setOptimizationStatus(statusRes.data.data);
      setSchedulerStatus(schedulerRes.data.data);
      setAutoOptEnabled(schedulerRes.data.data.is_running);
    } catch (error) {
      console.error('加载自动优化数据失败:', error);
    }
  };

  const handleToggleAutoOptimization = async (enabled: boolean) => {
    setLoading(true);
    try {
      const endpoint = enabled ? 'start' : 'stop';
      await axios.post(`/api/feedback/auto-optimize/scheduler/${endpoint}`);
      
      setAutoOptEnabled(enabled);
      message.success(`自动优化已${enabled ? '启用' : '停用'}`);
      
      // 刷新状态
      setTimeout(loadOptimizationData, 1000);
    } catch (error) {
      console.error('切换自动优化失败:', error);
      message.error('操作失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleTriggerOptimization = async () => {
    setLoading(true);
    try {
      const response = await axios.post('/api/feedback/auto-optimize/trigger');
      message.success('优化已触发，正在执行...');
      
      // 刷新状态
      setTimeout(loadOptimizationData, 2000);
    } catch (error) {
      console.error('触发优化失败:', error);
      message.error('触发失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleAnalyzeFeedback = async () => {
    setLoading(true);
    try {
      const response = await axios.post('/api/feedback/auto-optimize/analyze');
      setPendingActions(response.data.data.optimization_actions || []);
      setUserFeedback(response.data.data.user_feedback || []);
      setShowAnalysisModal(true);
      message.success(`分析完成，基于 ${response.data.data.total_feedback} 条反馈生成了 ${response.data.data.total_actions} 个优化建议`);
    } catch (error) {
      console.error('分析反馈失败:', error);
      message.error('分析失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 🆕 批量导出功能（增强版）
  const handleBatchExportMarkdown = async () => {
    setBatchExportLoading(prev => ({ ...prev, markdown: true }));

    try {
      if (pendingActions.length === 0) {
        message.warning('暂无优化建议可导出');
        return;
      }

      // 显示进度提示
      const hideLoading = message.loading(`正在批量生成 ${pendingActions.length} 个优化建议的Markdown文件...`, 0);

      const exportData = prepareExportData(pendingActions as ExportOptimizationAction[]);
      const markdownContent = formatTechnicalSpecToMarkdown(exportData);
      const period = pendingActions[0]?.primary_source_period || '未知期号';
      const filename = `fucai3d_batch_optimization_specs_${period}_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}.md`;

      // 模拟批量处理时间
      await new Promise(resolve => setTimeout(resolve, 1200));

      downloadFile(markdownContent, filename, 'text/markdown');

      hideLoading();

      message.success({
        content: `🎉 批量导出成功！已下载 ${pendingActions.length} 个优化建议的技术规格`,
        duration: 5,
        style: { marginTop: '20vh' }
      });
    } catch (error) {
      console.error('批量导出Markdown失败:', error);
      message.error({
        content: '❌ 批量导出失败，请稍后重试',
        duration: 3
      });
    } finally {
      setBatchExportLoading(prev => ({ ...prev, markdown: false }));
    }
  };

  const handleBatchExportJSON = async () => {
    setBatchExportLoading(prev => ({ ...prev, json: true }));

    try {
      if (pendingActions.length === 0) {
        message.warning('暂无优化建议可导出');
        return;
      }

      // 显示进度提示
      const hideLoading = message.loading(`正在批量生成 ${pendingActions.length} 个优化建议的JSON文件...`, 0);

      const exportData = prepareExportData(pendingActions as ExportOptimizationAction[]);
      const jsonContent = formatTechnicalSpecToJSON(exportData);
      const period = pendingActions[0]?.primary_source_period || '未知期号';
      const filename = `fucai3d_batch_optimization_specs_${period}_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}.json`;

      // 模拟批量处理时间
      await new Promise(resolve => setTimeout(resolve, 1000));

      downloadFile(jsonContent, filename, 'application/json');

      hideLoading();

      message.success({
        content: `🎉 批量导出成功！已下载 ${pendingActions.length} 个优化建议的技术规格`,
        duration: 5,
        style: { marginTop: '20vh' }
      });
    } catch (error) {
      console.error('批量导出JSON失败:', error);
      message.error({
        content: '❌ 批量导出失败，请稍后重试',
        duration: 3
      });
    } finally {
      setBatchExportLoading(prev => ({ ...prev, json: false }));
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'red';
      default: return 'orange';
    }
  };

  const getPriorityColor = (priority: number) => {
    if (priority >= 4) return 'red';
    if (priority >= 3) return 'orange';
    return 'blue';
  };

  const formatTime = (timeStr: string | null) => {
    if (!timeStr) return '未执行';
    if (timeStr === '立即') return '立即';
    return new Date(timeStr).toLocaleString();
  };

  const renderOptimizationOverview = () => (
    <Row gutter={[16, 16]}>
      <Col span={6}>
        <Card>
          <Statistic
            title="自动优化引擎"
            value={optimizationStatus?.engine_status || 'unknown'}
            prefix={<RobotOutlined />}
            valueStyle={{ color: getStatusColor(optimizationStatus?.engine_status || '') }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="总执行次数"
            value={optimizationStatus?.total_executions || 0}
            prefix={<ThunderboltOutlined />}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="优化规则数"
            value={optimizationStatus?.optimization_rules_count || 0}
            prefix={<SettingOutlined />}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="调度器状态"
            value={schedulerStatus?.is_running ? '运行中' : '已停止'}
            prefix={schedulerStatus?.is_running ? <PlayCircleOutlined /> : <PauseCircleOutlined />}
            valueStyle={{ color: schedulerStatus?.is_running ? 'green' : 'red' }}
          />
        </Card>
      </Col>
    </Row>
  );

  const renderDualEngineStatus = () => {
    const dualEngine = optimizationStatus?.dual_engine_status;

    if (!dualEngine) {
      return (
        <Card title="🔧 双引擎自动优化系统" style={{ marginTop: 16 }}>
          <Alert
            message="双引擎状态数据加载中..."
            type="info"
            showIcon
          />
        </Card>
      );
    }

    return (
      <Card title="🔧 双引擎自动优化系统" style={{ marginTop: 16 }}>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card size="small" title="⚡ 快速优化引擎">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Statistic
                  title="状态"
                  value={dualEngine.quick_optimization.status}
                  prefix={<ThunderboltOutlined />}
                  valueStyle={{
                    color: dualEngine.quick_optimization.status === 'active' ? 'green' : 'orange',
                    fontSize: '14px'
                  }}
                />
                <Text type="secondary">
                  成功率: {(dualEngine.quick_optimization.success_rate * 100).toFixed(1)}%
                </Text>
                <Text type="secondary">
                  上次执行: {formatTime(dualEngine.quick_optimization.last_execution)}
                </Text>
              </Space>
            </Card>
          </Col>

          <Col span={8}>
            <Card size="small" title="🧠 深度优化引擎">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Statistic
                  title="状态"
                  value={dualEngine.deep_optimization.status}
                  prefix={<BulbOutlined />}
                  valueStyle={{
                    color: dualEngine.deep_optimization.status === 'active' ? 'green' : 'orange',
                    fontSize: '14px'
                  }}
                />
                <Text type="secondary">
                  SHAP分析次数: {dualEngine.deep_optimization.shap_analysis_count}
                </Text>
                <Text type="secondary">
                  上次执行: {formatTime(dualEngine.deep_optimization.last_execution)}
                </Text>
              </Space>
            </Card>
          </Col>

          <Col span={8}>
            <Card size="small" title="🔍 智能反馈解析器">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Statistic
                  title="已解析反馈"
                  value={dualEngine.feedback_parser.total_parsed}
                  prefix={<EyeOutlined />}
                  valueStyle={{ fontSize: '14px' }}
                />
                <Text type="secondary">
                  自动执行率: {(dualEngine.feedback_parser.auto_actionable_rate * 100).toFixed(1)}%
                </Text>
                <Tooltip title={dualEngine.feedback_parser.last_parsed_feedback}>
                  <Text type="secondary" ellipsis>
                    最近解析: {dualEngine.feedback_parser.last_parsed_feedback?.substring(0, 20)}...
                  </Text>
                </Tooltip>
              </Space>
            </Card>
          </Col>
        </Row>

        <Divider />

        <Alert
          message="双引擎自动优化系统"
          description="快速优化引擎负责基于用户反馈的即时优化，深度优化引擎负责SHAP分析和模型重训练，智能反馈解析器负责从用户反馈中提取关键信息并触发相应的优化行动。"
          type="info"
          showIcon
          icon={<RobotOutlined />}
        />
      </Card>
    );
  };

  const renderControlPanel = () => (
    <Card title="🎛️ 自动优化控制面板" style={{ marginTop: 16 }}>
      <Row gutter={[16, 16]} align="middle">
        <Col span={8}>
          <Space direction="vertical">
            <Text strong>自动优化开关</Text>
            <Switch
              checked={autoOptEnabled}
              onChange={handleToggleAutoOptimization}
              loading={loading}
              checkedChildren="启用"
              unCheckedChildren="停用"
            />
            <Text type="secondary">
              {autoOptEnabled ? '系统将自动根据用户反馈进行优化' : '自动优化已停用'}
            </Text>
          </Space>
        </Col>
        <Col span={8}>
          <Space direction="vertical">
            <Text strong>立即触发优化</Text>
            <Button
              type="primary"
              icon={<FireOutlined />}
              onClick={handleTriggerOptimization}
              loading={loading}
            >
              立即优化
            </Button>
            <Text type="secondary">手动触发一次完整的优化周期</Text>
          </Space>
        </Col>
        <Col span={8}>
          <Space direction="vertical">
            <Text strong>分析用户反馈</Text>
            <Button
              icon={<EyeOutlined />}
              onClick={handleAnalyzeFeedback}
              loading={loading}
            >
              分析反馈
            </Button>
            <Text type="secondary">分析当前反馈并生成优化建议</Text>
          </Space>
        </Col>
      </Row>
    </Card>
  );

  const renderSchedulerInfo = () => (
    <Card title="⏰ 调度器信息" style={{ marginTop: 16 }}>
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text strong>优化间隔</Text>
            <Text>{Math.floor((schedulerStatus?.optimization_interval || 0) / 60)} 分钟</Text>
            
            <Text strong>上次优化时间</Text>
            <Text>{formatTime(schedulerStatus?.last_optimization_time)}</Text>
            
            <Text strong>下次优化时间</Text>
            <Text>{formatTime(schedulerStatus?.next_optimization_time)}</Text>
          </Space>
        </Col>
        <Col span={12}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text strong>已执行优化次数</Text>
            <Text>{schedulerStatus?.optimization_count || 0} 次</Text>
            
            <Text strong>自动优化状态</Text>
            <Badge 
              status={schedulerStatus?.auto_optimization_enabled ? 'processing' : 'default'}
              text={schedulerStatus?.auto_optimization_enabled ? '已启用' : '已禁用'}
            />
          </Space>
        </Col>
      </Row>
    </Card>
  );

  const renderRecentExecutions = () => (
    <Card title="📋 最近执行记录" style={{ marginTop: 16 }}>
      {optimizationStatus?.recent_executions?.length ? (
        <Timeline>
          {optimizationStatus.recent_executions.map((execution, index) => (
            <Timeline.Item
              key={execution.action_id}
              color={execution.success ? 'green' : 'red'}
              dot={execution.success ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
            >
              <Space direction="vertical" size="small">
                <Text strong>{execution.success ? '执行成功' : '执行失败'}</Text>
                <Text type="secondary">{execution.notes}</Text>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {new Date(execution.execution_time).toLocaleString()}
                </Text>
              </Space>
            </Timeline.Item>
          ))}
        </Timeline>
      ) : (
        <Text type="secondary">暂无执行记录</Text>
      )}
    </Card>
  );

  const renderOptimizationRules = () => (
    <Card title="🧠 优化规则库" style={{ marginTop: 16 }}>
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card size="small">
            <Space direction="vertical" align="center" style={{ width: '100%' }}>
              <TrophyOutlined style={{ fontSize: 24, color: '#1890ff' }} />
              <Text strong>预测准确率优化</Text>
              <Text type="secondary" style={{ textAlign: 'center' }}>
                基于准确率反馈自动调整算法参数
              </Text>
            </Space>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Space direction="vertical" align="center" style={{ width: '100%' }}>
              <BulbOutlined style={{ fontSize: 24, color: '#52c41a' }} />
              <Text strong>界面优化</Text>
              <Text type="secondary" style={{ textAlign: 'center' }}>
                根据易用性反馈简化操作流程
              </Text>
            </Space>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Space direction="vertical" align="center" style={{ width: '100%' }}>
              <SettingOutlined style={{ fontSize: 24, color: '#faad14' }} />
              <Text strong>功能完整性</Text>
              <Text type="secondary" style={{ textAlign: 'center' }}>
                基于功能需求自动启用新特性
              </Text>
            </Space>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Space direction="vertical" align="center" style={{ width: '100%' }}>
              <RobotOutlined style={{ fontSize: 24, color: '#eb2f96' }} />
              <Text strong>自动优化</Text>
              <Text type="secondary" style={{ textAlign: 'center' }}>
                持续改进和自我优化机制
              </Text>
            </Space>
          </Card>
        </Col>
      </Row>
    </Card>
  );

  return (
    <div style={{ padding: '20px' }}>
      <Title level={2}>
        <RobotOutlined style={{ marginRight: 8, color: '#1890ff' }} />
        自动优化系统
      </Title>
      <Paragraph type="secondary">
        基于用户反馈的智能自动优化系统，实现系统的持续改进和迭代。
      </Paragraph>

      <Alert
        message="🤖 智能自动优化"
        description="系统会自动分析用户反馈，识别问题和改进机会，并自动执行优化措施，无需人工干预。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {renderOptimizationOverview()}
      {renderDualEngineStatus()}
      {renderControlPanel()}
      {renderSchedulerInfo()}
      {renderOptimizationRules()}
      {renderRecentExecutions()}

      <Modal
        title="📊 反馈分析结果"
        open={showAnalysisModal}
        onCancel={() => setShowAnalysisModal(false)}
        footer={[
          <Button key="close" onClick={() => setShowAnalysisModal(false)}>
            关闭
          </Button>
        ]}
        width={1000}
      >
        <Tabs defaultActiveKey="feedback" items={[
          {
            key: 'feedback',
            label: '📝 用户反馈',
            children: (
              <div style={{ maxHeight: 400, overflowY: 'auto' }}>
                <List
                  dataSource={userFeedback}
                  renderItem={(feedback) => (
                    <List.Item>
                      <List.Item.Meta
                        title={
                          <Space>
                            <Text strong>
                              {feedback.prediction_period ? `${feedback.prediction_period}期` : '反馈'}
                            </Text>
                            <Tag color="blue">{feedback.feedback_type}</Tag>
                            {feedback.rating && (
                              <Tag color="orange">{feedback.rating}星</Tag>
                            )}
                          </Space>
                        }
                        description={
                          <Space direction="vertical" size="small" style={{ width: '100%' }}>
                            <Text>{feedback.content}</Text>
                            {feedback.prediction_numbers && feedback.actual_numbers && (
                              <Text type="secondary">
                                预测: {feedback.prediction_numbers} → 实际: {feedback.actual_numbers}
                              </Text>
                            )}
                            <Text type="secondary">
                              提交时间: {new Date(feedback.created_at).toLocaleString()}
                            </Text>
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                  locale={{ emptyText: '暂无用户反馈' }}
                />
              </div>
            )
          },
          {
            key: 'actions',
            label: '🔧 优化建议',
            children: (
              <div>
                {/* 🆕 批量导出按钮（增强版） */}
                {pendingActions.length > 0 && (
                  <Card size="small" style={{ marginBottom: 16, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f' }}>
                    <Space>
                      <Text strong>📤 批量导出技术规格：</Text>
                      <Tooltip title="导出所有优化建议的Markdown格式技术规格，适合发送给Augment">
                        <Button
                          type="primary"
                          icon={<FileMarkdownOutlined />}
                          onClick={handleBatchExportMarkdown}
                          loading={batchExportLoading.markdown}
                          disabled={batchExportLoading.json}
                          size="small"
                        >
                          {batchExportLoading.markdown ? '批量生成中...' : `导出全部MD (${pendingActions.length}个)`}
                        </Button>
                      </Tooltip>
                      <Tooltip title="导出所有优化建议的JSON格式技术规格">
                        <Button
                          icon={<FileTextOutlined />}
                          onClick={handleBatchExportJSON}
                          loading={batchExportLoading.json}
                          disabled={batchExportLoading.markdown}
                          size="small"
                        >
                          {batchExportLoading.json ? '批量生成中...' : '导出全部JSON'}
                        </Button>
                      </Tooltip>
                      <Text type="secondary">
                        包含完整的技术参数、执行策略、Claude提示和执行命令
                      </Text>
                      {(batchExportLoading.markdown || batchExportLoading.json) && (
                        <Text type="secondary" style={{ fontSize: '12px', color: '#1890ff' }}>
                          正在批量处理 {pendingActions.length} 个优化建议，请稍候...
                        </Text>
                      )}
                    </Space>
                  </Card>
                )}

                <div style={{ maxHeight: 600, overflowY: 'auto' }}>
                  {pendingActions.length > 0 ? (
                    <Space direction="vertical" style={{ width: '100%' }} size="large">
                      {pendingActions.map((action, index) => (
                        <OptimizationExplanation
                          key={action.action_id || index}
                          action={action}
                          viewMode={explanationViewMode}
                          onViewModeChange={setExplanationViewMode}
                        />
                      ))}
                    </Space>
                  ) : (
                    <Alert
                      message="暂无优化建议"
                      description="当前没有待处理的优化建议，系统运行正常。"
                      type="info"
                      showIcon
                    />
                  )}
                </div>
              </div>
            )
          }
        ]} />
      </Modal>
    </div>
  );
};

export default AutoOptimizationDashboard;
