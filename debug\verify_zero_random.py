#!/usr/bin/env python3
"""
简化的零随机因子验证脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_random_imports():
    """检查SmartRecommendationEngine中是否还有random导入"""
    print("=== 检查random导入 ===")
    
    engine_file = project_root / "src" / "fusion" / "smart_recommendation_engine.py"
    if not engine_file.exists():
        print("❌ SmartRecommendationEngine文件不存在")
        return False
    
    with open(engine_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查random导入
    random_imports = []
    lines = content.split('\n')
    for i, line in enumerate(lines, 1):
        if 'import random' in line and not line.strip().startswith('#'):
            random_imports.append(f"第{i}行: {line.strip()}")
        elif 'from random' in line and not line.strip().startswith('#'):
            random_imports.append(f"第{i}行: {line.strip()}")
    
    if random_imports:
        print("❌ 发现random导入:")
        for imp in random_imports:
            print(f"  {imp}")
        return False
    else:
        print("✅ 未发现random导入")
    
    # 检查random调用
    random_calls = []
    for i, line in enumerate(lines, 1):
        if 'random.' in line and not line.strip().startswith('#'):
            random_calls.append(f"第{i}行: {line.strip()}")
    
    if random_calls:
        print("❌ 发现random调用:")
        for call in random_calls:
            print(f"  {call}")
        return False
    else:
        print("✅ 未发现random调用")
    
    return True

def test_deterministic_behavior():
    """测试确定性行为"""
    print("\n=== 测试确定性行为 ===")
    
    try:
        from src.fusion.smart_recommendation_engine import SmartRecommendationEngine
        
        # 创建引擎实例
        engine = SmartRecommendationEngine()
        print("✅ SmartRecommendationEngine初始化成功")
        
        # 测试位置推荐的确定性
        print("\n测试位置推荐确定性:")
        for position in ['hundreds', 'tens', 'units']:
            try:
                result1 = engine.generate_position_recommendation(position)
                result2 = engine.generate_position_recommendation(position)
                
                recs1 = result1.get('recommendations', [])
                recs2 = result2.get('recommendations', [])
                
                if recs1 and recs2:
                    num1 = recs1[0].get('number')
                    num2 = recs2[0].get('number')
                    
                    if num1 == num2:
                        print(f"  ✅ {position}位置推荐一致: {num1}")
                    else:
                        print(f"  ❌ {position}位置推荐不一致: {num1} vs {num2}")
                        return False
                else:
                    print(f"  ⚠️ {position}位置推荐为空")
            except Exception as e:
                print(f"  ❌ {position}位置推荐失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_historical_adjustment():
    """测试历史调整引擎"""
    print("\n=== 测试历史调整引擎 ===")
    
    try:
        from src.fusion.historical_adjustment_engine import HistoricalAdjustmentEngine
        
        engine = HistoricalAdjustmentEngine()
        print("✅ HistoricalAdjustmentEngine初始化成功")
        
        # 测试相同输入产生相同输出
        test_cases = [
            (5, "2025214", "hundreds"),
            (3, "2025214", "tens"),
            (7, "2025214", "units")
        ]
        
        for number, issue, position in test_cases:
            try:
                adj1 = engine.calculate_issue_sensitivity_adjustment(number, issue, position)
                adj2 = engine.calculate_issue_sensitivity_adjustment(number, issue, position)
                
                if adj1 == adj2:
                    print(f"  ✅ 数字{number}在{position}位置调整一致: {adj1}")
                else:
                    print(f"  ❌ 数字{number}在{position}位置调整不一致: {adj1} vs {adj2}")
                    return False
            except Exception as e:
                print(f"  ❌ 调整计算失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 历史调整引擎测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 零随机因子验证开始")
    print("=" * 50)
    
    success = True
    
    # 检查random导入
    if not check_random_imports():
        success = False
    
    # 测试确定性行为
    if not test_deterministic_behavior():
        success = False
    
    # 测试历史调整引擎
    if not test_historical_adjustment():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 零随机因子验证通过！")
        print("✅ 所有随机因子已成功移除")
        print("✅ 系统行为完全确定性")
        print("✅ 基于历史数据的调整正常工作")
    else:
        print("❌ 零随机因子验证失败！")
        print("⚠️ 仍存在随机因子或确定性问题")
    
    return success

if __name__ == "__main__":
    main()
