#!/usr/bin/env python3
"""
检查数据同步状态
"""

import sqlite3
from datetime import datetime

def check_sync_status():
    """检查数据同步状态"""
    try:
        # 检查主数据库
        print("=== 检查主数据库 (lottery.db) ===")
        conn = sqlite3.connect("data/lottery.db")
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT issue, draw_date, hundreds, tens, units, updated_at
            FROM lottery_data 
            ORDER BY id DESC 
            LIMIT 5
        """)
        
        main_records = cursor.fetchall()
        print("最新5条记录:")
        for record in main_records:
            issue, draw_date, h, t, u, updated_at = record
            print(f"期号: {issue}, 开奖: {h}{t}{u}, 日期: {draw_date}, 更新: {updated_at}")
        
        conn.close()
        
        # 检查预测数据库
        print("\n=== 检查预测数据库 (fucai3d.db) ===")
        conn = sqlite3.connect("data/fucai3d.db")
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT issue, draw_date, hundreds, tens, units, updated_at
            FROM lottery_data 
            ORDER BY id DESC 
            LIMIT 5
        """)
        
        pred_records = cursor.fetchall()
        print("最新5条记录:")
        for record in pred_records:
            issue, draw_date, h, t, u, updated_at = record
            print(f"期号: {issue}, 开奖: {h}{t}{u}, 日期: {draw_date}, 更新: {updated_at}")
        
        # 检查2025214期是否存在
        print("\n=== 检查2025214期数据 ===")
        cursor.execute("""
            SELECT issue, draw_date, hundreds, tens, units, updated_at
            FROM lottery_data 
            WHERE issue = '2025214'
        """)
        
        record_214 = cursor.fetchone()
        if record_214:
            issue, draw_date, h, t, u, updated_at = record_214
            print(f"找到2025214期: 开奖 {h}{t}{u}, 日期: {draw_date}, 更新: {updated_at}")
        else:
            print("未找到2025214期数据")
        
        conn.close()
        
        # 检查期号生成逻辑
        print("\n=== 测试期号生成逻辑 ===")
        from datetime import datetime
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        try:
            from src.fusion.smart_recommendation_engine import SmartRecommendationEngine
            engine = SmartRecommendationEngine()
            issue_info = engine._get_next_issue_info()
            print(f"期号信息: {issue_info}")
        except Exception as e:
            print(f"期号生成测试失败: {e}")
        
    except Exception as e:
        print(f"检查失败: {e}")

if __name__ == "__main__":
    check_sync_status()
