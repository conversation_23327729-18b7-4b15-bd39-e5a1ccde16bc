#!/usr/bin/env python3
"""
预测结果保存器
将智能推荐结果转换为final_predictions表格式并保存
同时保存到预测复盘相关表

Author: Augment Code AI Assistant
Date: 2025-08-13
"""

import sqlite3
import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)

class PredictionSaver:
    """预测结果保存器"""
    
    def __init__(self, db_path: str = "data/fucai3d.db"):
        self.db_path = db_path
    
    def save_recommendation_to_predictions(self, recommendations: Dict[str, Any], issue: str) -> bool:
        """
        将智能推荐结果保存到final_predictions表
        
        Args:
            recommendations: 智能推荐结果
            issue: 期号
            
        Returns:
            bool: 保存是否成功
        """
        try:
            if recommendations.get('status') != 'success':
                logger.error(f"推荐结果状态异常: {recommendations.get('status')}")
                return False
            
            # 提取推荐数据 - 修复数据结构匹配问题
            # SmartRecommendationEngine返回的结构是直接的，不在'data'字段下
            combination = recommendations.get('combination', {})
            if not combination:
                logger.error("推荐结果中没有组合数据")
                return False

            # 获取推荐的组合
            recommended_combinations = combination.get('recommendations', [])
            if not recommended_combinations:
                logger.error("推荐结果中没有具体组合")
                return False

            # 获取整体置信度
            overall_confidence = recommendations.get('overall_confidence', 0.5)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 清除该期号的旧预测数据
            cursor.execute("DELETE FROM final_predictions WHERE issue = ?", (issue,))
            
            # 保存推荐组合到final_predictions表
            saved_count = 0
            for rank, combo in enumerate(recommended_combinations[:10], 1):  # 最多保存10个
                combination_str = combo.get('combination', '')
                if len(combination_str) == 3:
                    hundreds = int(combination_str[0])
                    tens = int(combination_str[1])
                    units = int(combination_str[2])
                    
                    # 计算和值和跨度
                    sum_value = hundreds + tens + units
                    span_value = max(hundreds, tens, units) - min(hundreds, tens, units)
                    
                    # 获取置信度数值
                    confidence_value = combo.get('confidence', overall_confidence)
                    # 确保置信度是数值类型
                    if confidence_value is None:
                        confidence_value = overall_confidence if overall_confidence is not None else 0.5

                    # 转换置信度为文本等级
                    confidence_level = self.calculate_confidence_level(confidence_value)
                    
                    # 计算各种概率和分数（使用默认值确保NOT NULL约束）
                    combo_confidence = combo.get('confidence', overall_confidence)

                    # 插入数据，填充所有必需字段
                    cursor.execute("""
                        INSERT INTO final_predictions (
                            issue, prediction_rank, hundreds, tens, units,
                            sum_value, span_value, combined_probability,
                            hundreds_prob, tens_prob, units_prob,
                            sum_prob, span_prob, sum_consistency, span_consistency,
                            constraint_score, diversity_score,
                            confidence_level, fusion_method, ranking_strategy,
                            created_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        issue, rank, hundreds, tens, units,
                        sum_value, span_value, combo_confidence,
                        combo_confidence, combo_confidence, combo_confidence,  # 各位概率
                        combo_confidence, combo_confidence, combo_confidence, combo_confidence,  # 和值跨度概率和一致性
                        combo_confidence * 100, combo_confidence * 80,  # 约束分数和多样性分数
                        confidence_level, 'smart_recommendation', 'shap_driven',
                        datetime.now().isoformat()
                    ))
                    saved_count += 1
            
            conn.commit()
            
            # 同时保存到预测复盘表
            self.save_to_review_table(recommendations, issue)
            
            conn.close()
            
            logger.info(f"成功保存 {saved_count} 个预测结果到final_predictions表，期号: {issue}")
            return True
            
        except Exception as e:
            logger.error(f"保存推荐结果失败: {e}")
            return False
    
    def calculate_confidence_level(self, confidence_score: float) -> str:
        """
        将数值置信度转换为文本等级
        
        Args:
            confidence_score: 数值置信度 (0.0-1.0)
            
        Returns:
            str: 置信度等级文本
        """
        if confidence_score >= 0.8:
            return "高"
        elif confidence_score >= 0.6:
            return "中"
        elif confidence_score >= 0.4:
            return "低"
        else:
            return "极低"
    
    def calculate_confidence_score(self, recommendation: Dict[str, Any]) -> float:
        """
        计算推荐的置信度分数
        
        Args:
            recommendation: 推荐数据
            
        Returns:
            float: 置信度分数 (0.0-1.0)
        """
        try:
            # 基础置信度
            base_confidence = recommendation.get('confidence', 0.5)
            
            # 考虑星级评分
            star_rating = recommendation.get('star_rating', 3)
            star_bonus = (star_rating - 3) * 0.1  # 星级调整
            
            # 考虑风险等级
            risk_level = recommendation.get('risk_level', 'medium')
            risk_adjustment = {
                'low': 0.1,
                'medium': 0.0,
                'high': -0.1
            }.get(risk_level, 0.0)
            
            # 综合计算
            final_confidence = base_confidence + star_bonus + risk_adjustment
            
            # 确保在合理范围内
            return max(0.0, min(1.0, final_confidence))
            
        except Exception as e:
            logger.error(f"计算置信度分数失败: {e}")
            return 0.5
    
    def save_to_review_table(self, recommendations: Dict[str, Any], issue: str) -> bool:
        """
        保存到预测复盘表
        
        Args:
            recommendations: 推荐结果
            issue: 期号
            
        Returns:
            bool: 保存是否成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取推荐组合 - 修复数据结构
            combination = recommendations.get('combination', {})
            recommended_combinations = combination.get('recommendations', [])
            
            if not recommended_combinations:
                return False
            
            # 保存最佳推荐到learning_prediction_records表
            best_combo = recommended_combinations[0]
            combination_str = best_combo.get('combination', '')
            
            if len(combination_str) == 3:
                prediction_id = str(uuid.uuid4())
                confidence_score = self.calculate_confidence_score(best_combo)
                
                cursor.execute("""
                    INSERT INTO learning_prediction_records (
                        prediction_id, issue, predicted_numbers, confidence_level,
                        prediction_date, model_source, strategy_id, metadata
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    prediction_id, issue, combination_str, confidence_score,
                    datetime.now().isoformat(), 'smart_recommendation_engine',
                    'shap_driven_adaptive', json.dumps({
                        'overall_confidence': recommendations.get('overall_confidence'),
                        'algorithm_version': recommendations.get('algorithm_version'),
                        'star_rating': best_combo.get('star_rating'),
                        'risk_level': best_combo.get('risk_level')
                    })
                ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"成功保存预测复盘数据，期号: {issue}")
            return True
            
        except Exception as e:
            logger.error(f"保存预测复盘数据失败: {e}")
            return False
    
    def get_latest_predictions(self, issue: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        获取指定期号的最新预测结果
        
        Args:
            issue: 期号
            limit: 返回数量限制
            
        Returns:
            List[Dict]: 预测结果列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT hundreds, tens, units, combined_probability, confidence_level
                FROM final_predictions
                WHERE issue = ?
                ORDER BY prediction_rank ASC
                LIMIT ?
            """, (issue, limit))
            
            results = []
            for row in cursor.fetchall():
                hundreds, tens, units, prob, confidence = row
                results.append({
                    'hundreds': hundreds,
                    'tens': tens,
                    'units': units,
                    'combined_probability': prob,
                    'confidence_level': confidence
                })
            
            conn.close()
            return results
            
        except Exception as e:
            logger.error(f"获取预测结果失败: {e}")
            return []
