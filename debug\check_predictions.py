#!/usr/bin/env python3
"""
检查预测数据脚本
"""

import sqlite3
import os

def check_predictions():
    """检查预测数据"""
    db_path = "data/fucai3d.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查2025215期的预测数据
        print("=== 2025215期预测数据 ===")
        cursor.execute("""
            SELECT issue, hundreds, tens, units, confidence_level, created_at 
            FROM final_predictions 
            WHERE issue = '2025215' 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        predictions = cursor.fetchall()
        if predictions:
            for row in predictions:
                issue, hundreds, tens, units, confidence, created_at = row
                print(f"期号: {issue}, 预测: {hundreds}{tens}{units}, 置信度: {confidence}, 时间: {created_at}")
        else:
            print("没有找到2025215期的预测数据")
        
        # 检查最新的预测数据
        print("\n=== 最新预测数据 ===")
        cursor.execute("""
            SELECT issue, hundreds, tens, units, confidence_level, created_at 
            FROM final_predictions 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        recent_predictions = cursor.fetchall()
        for row in recent_predictions:
            issue, hundreds, tens, units, confidence, created_at = row
            print(f"期号: {issue}, 预测: {hundreds}{tens}{units}, 置信度: {confidence}, 时间: {created_at}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查预测数据时出错: {e}")

if __name__ == "__main__":
    check_predictions()
