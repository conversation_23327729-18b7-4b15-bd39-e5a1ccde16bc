#!/usr/bin/env python3
"""
测试预测保存功能
"""

import sys
import os
import json
import requests

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.fusion.prediction_saver import PredictionSaver

def test_prediction_save():
    """测试预测保存功能"""
    
    # 1. 调用智能推荐API获取数据
    print("=== 调用智能推荐API ===")
    try:
        response = requests.get("http://127.0.0.1:8000/api/smart-recommend/daily")
        if response.status_code == 200:
            recommendations = response.json()
            print(f"API调用成功，状态: {recommendations.get('status')}")
            print(f"推荐组合: {recommendations.get('data', {}).get('combination', {}).get('combination')}")
        else:
            print(f"API调用失败，状态码: {response.status_code}")
            return
    except Exception as e:
        print(f"API调用出错: {e}")
        return
    
    # 2. 手动保存数据
    print("\n=== 手动保存预测数据 ===")
    try:
        saver = PredictionSaver()
        issue = recommendations.get('data', {}).get('issue_info', {}).get('issue', '2025215')
        
        # 构造保存数据的格式
        save_data = {
            'status': 'success',
            'data': recommendations.get('data', {})
        }
        
        success = saver.save_recommendation_to_predictions(save_data, issue)
        if success:
            print(f"数据保存成功，期号: {issue}")
        else:
            print(f"数据保存失败，期号: {issue}")
            
    except Exception as e:
        print(f"数据保存出错: {e}")
        import traceback
        traceback.print_exc()
    
    # 3. 验证保存结果
    print("\n=== 验证保存结果 ===")
    try:
        import sqlite3
        conn = sqlite3.connect("data/fucai3d.db")
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT issue, hundreds, tens, units, confidence_level, created_at 
            FROM final_predictions 
            WHERE issue = '2025215' 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        results = cursor.fetchall()
        if results:
            print("最新保存的预测数据:")
            for row in results:
                issue, hundreds, tens, units, confidence, created_at = row
                print(f"期号: {issue}, 预测: {hundreds}{tens}{units}, 置信度: {confidence}, 时间: {created_at}")
        else:
            print("没有找到新保存的数据")
        
        conn.close()
        
    except Exception as e:
        print(f"验证保存结果出错: {e}")

if __name__ == "__main__":
    test_prediction_save()
