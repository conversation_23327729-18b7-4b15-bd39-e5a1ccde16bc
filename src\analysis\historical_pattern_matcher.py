#!/usr/bin/env python3
"""
历史模式匹配算法
识别历史相似期号、分析相似情况表现、生成基于历史证据的推荐理由
"""

import sqlite3
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
import json
import os
from pathlib import Path
from collections import defaultdict
import math

logger = logging.getLogger(__name__)


class HistoricalPatternMatcher:
    """
    历史模式匹配算法
    
    通过分析历史数据中的模式，为当前预测提供
    基于历史证据的推荐理由和相似案例分析。
    """
    
    def __init__(self, db_path: str = "data/fucai3d.db"):
        """
        初始化历史模式匹配器
        
        Args:
            db_path: 数据库路径
        """
        # 确保使用绝对路径
        if not os.path.isabs(db_path):
            current_dir = Path(__file__).parent.parent.parent
            self.db_path = str(current_dir / db_path)
        else:
            self.db_path = db_path
            
        # 加载历史数据
        self._load_historical_data()
        
        # 预计算模式
        self._precompute_patterns()
        
        logger.info("历史模式匹配算法初始化完成")
    
    def _load_historical_data(self):
        """加载历史数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = """
                    SELECT issue, draw_date, hundreds, tens, units, sum_value, span, number_type
                    FROM lottery_data
                    ORDER BY id ASC
                """
                self.historical_df = pd.read_sql_query(query, conn)
                
                if self.historical_df.empty:
                    logger.warning("数据库中没有历史数据")
                    return
                
                # 数据预处理
                self.historical_df['draw_date'] = pd.to_datetime(self.historical_df['draw_date'])
                self.historical_df['month'] = self.historical_df['draw_date'].dt.month
                self.historical_df['weekday'] = self.historical_df['draw_date'].dt.weekday
                self.historical_df['day_of_year'] = self.historical_df['draw_date'].dt.dayofyear
                
                logger.info(f"加载历史数据成功: {len(self.historical_df)} 条记录")
                
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            self.historical_df = pd.DataFrame()
    
    def _precompute_patterns(self):
        """预计算常用模式"""
        try:
            if self.historical_df.empty:
                return
            
            # 计算各种模式
            self.frequency_patterns = self._compute_frequency_patterns()
            self.sequence_patterns = self._compute_sequence_patterns()
            self.cyclical_patterns = self._compute_cyclical_patterns()
            self.correlation_patterns = self._compute_correlation_patterns()
            
            logger.info("模式预计算完成")
            
        except Exception as e:
            logger.error(f"预计算模式失败: {e}")
    
    def _compute_frequency_patterns(self) -> Dict[str, Any]:
        """计算频率模式"""
        try:
            patterns = {}
            
            for position in ['hundreds', 'tens', 'units']:
                if position in self.historical_df.columns:
                    # 整体频率
                    freq_dist = self.historical_df[position].value_counts(normalize=True).to_dict()
                    
                    # 最近频率（最近100期）
                    recent_data = self.historical_df.tail(100)
                    recent_freq = recent_data[position].value_counts(normalize=True).to_dict()
                    
                    # 热号和冷号
                    hot_numbers = sorted(freq_dist.items(), key=lambda x: x[1], reverse=True)[:3]
                    cold_numbers = sorted(freq_dist.items(), key=lambda x: x[1])[:3]
                    
                    patterns[position] = {
                        'overall_frequency': freq_dist,
                        'recent_frequency': recent_freq,
                        'hot_numbers': hot_numbers,
                        'cold_numbers': cold_numbers
                    }
            
            return patterns
            
        except Exception as e:
            logger.error(f"计算频率模式失败: {e}")
            return {}
    
    def _compute_sequence_patterns(self) -> Dict[str, Any]:
        """计算序列模式"""
        try:
            patterns = {}
            
            for position in ['hundreds', 'tens', 'units']:
                if position in self.historical_df.columns:
                    sequence = self.historical_df[position].tolist()
                    
                    # 连续出现模式
                    consecutive = self._find_consecutive_occurrences(sequence)
                    
                    # 间隔模式
                    intervals = self._find_interval_patterns(sequence)
                    
                    # 周期性模式
                    cycles = self._find_cyclical_patterns(sequence)
                    
                    patterns[position] = {
                        'consecutive_patterns': consecutive,
                        'interval_patterns': intervals,
                        'cyclical_patterns': cycles
                    }
            
            return patterns
            
        except Exception as e:
            logger.error(f"计算序列模式失败: {e}")
            return {}
    
    def _find_consecutive_occurrences(self, sequence: List[int]) -> Dict[int, List[int]]:
        """查找连续出现模式"""
        try:
            consecutive_counts = defaultdict(list)
            
            current_num = None
            current_count = 0
            
            for num in sequence:
                if num == current_num:
                    current_count += 1
                else:
                    if current_count > 1:
                        consecutive_counts[current_num].append(current_count)
                    current_num = num
                    current_count = 1
            
            # 处理最后一个序列
            if current_count > 1:
                consecutive_counts[current_num].append(current_count)
            
            return dict(consecutive_counts)
            
        except Exception as e:
            logger.error(f"查找连续出现模式失败: {e}")
            return {}
    
    def _find_interval_patterns(self, sequence: List[int]) -> Dict[int, Dict[str, Any]]:
        """查找间隔模式"""
        try:
            interval_patterns = {}
            
            for num in range(10):
                positions = [i for i, x in enumerate(sequence) if x == num]
                
                if len(positions) > 1:
                    intervals = [positions[i+1] - positions[i] for i in range(len(positions)-1)]
                    
                    interval_patterns[num] = {
                        'intervals': intervals,
                        'avg_interval': np.mean(intervals),
                        'min_interval': min(intervals),
                        'max_interval': max(intervals),
                        'last_position': positions[-1],
                        'since_last': len(sequence) - positions[-1] - 1
                    }
            
            return interval_patterns
            
        except Exception as e:
            logger.error(f"查找间隔模式失败: {e}")
            return {}
    
    def _find_cyclical_patterns(self, sequence: List[int]) -> Dict[str, Any]:
        """查找周期性模式"""
        try:
            # 简化的周期性分析
            patterns = {}
            
            # 分析不同周期长度的重复模式
            for cycle_length in [7, 10, 14, 21, 30]:
                if len(sequence) >= cycle_length * 2:
                    cycle_matches = 0
                    total_cycles = len(sequence) // cycle_length
                    
                    for i in range(total_cycles - 1):
                        cycle1 = sequence[i * cycle_length:(i + 1) * cycle_length]
                        cycle2 = sequence[(i + 1) * cycle_length:(i + 2) * cycle_length]
                        
                        # 计算相似度
                        matches = sum(1 for a, b in zip(cycle1, cycle2) if a == b)
                        if matches / cycle_length > 0.3:  # 30%以上相似
                            cycle_matches += 1
                    
                    if total_cycles > 1:
                        patterns[f'cycle_{cycle_length}'] = cycle_matches / (total_cycles - 1)
            
            return patterns
            
        except Exception as e:
            logger.error(f"查找周期性模式失败: {e}")
            return {}
    
    def _compute_cyclical_patterns(self) -> Dict[str, Any]:
        """计算时间周期模式"""
        try:
            patterns = {}
            
            # 按月份分析
            monthly_patterns = {}
            for month in range(1, 13):
                month_data = self.historical_df[self.historical_df['month'] == month]
                if not month_data.empty:
                    monthly_patterns[month] = {}
                    for position in ['hundreds', 'tens', 'units']:
                        if position in month_data.columns:
                            freq_dist = month_data[position].value_counts(normalize=True).to_dict()
                            monthly_patterns[month][position] = freq_dist
            
            patterns['monthly'] = monthly_patterns
            
            # 按星期分析
            weekly_patterns = {}
            for weekday in range(7):
                week_data = self.historical_df[self.historical_df['weekday'] == weekday]
                if not week_data.empty:
                    weekly_patterns[weekday] = {}
                    for position in ['hundreds', 'tens', 'units']:
                        if position in week_data.columns:
                            freq_dist = week_data[position].value_counts(normalize=True).to_dict()
                            weekly_patterns[weekday][position] = freq_dist
            
            patterns['weekly'] = weekly_patterns
            
            return patterns
            
        except Exception as e:
            logger.error(f"计算时间周期模式失败: {e}")
            return {}
    
    def _compute_correlation_patterns(self) -> Dict[str, Any]:
        """计算相关性模式"""
        try:
            patterns = {}
            
            # 位置间相关性
            positions = ['hundreds', 'tens', 'units']
            correlations = {}
            
            for i, pos1 in enumerate(positions):
                for pos2 in positions[i+1:]:
                    if pos1 in self.historical_df.columns and pos2 in self.historical_df.columns:
                        corr = self.historical_df[pos1].corr(self.historical_df[pos2])
                        correlations[f"{pos1}_{pos2}"] = corr
            
            patterns['position_correlations'] = correlations
            
            # 和值与各位的相关性
            if 'sum_value' in self.historical_df.columns:
                sum_correlations = {}
                for position in positions:
                    if position in self.historical_df.columns:
                        corr = self.historical_df[position].corr(self.historical_df['sum_value'])
                        sum_correlations[position] = corr
                patterns['sum_correlations'] = sum_correlations
            
            # 跨度与各位的相关性
            if 'span' in self.historical_df.columns:
                span_correlations = {}
                for position in positions:
                    if position in self.historical_df.columns:
                        corr = self.historical_df[position].corr(self.historical_df['span'])
                        span_correlations[position] = corr
                patterns['span_correlations'] = span_correlations
            
            return patterns
            
        except Exception as e:
            logger.error(f"计算相关性模式失败: {e}")
            return {}
    
    def identify_similar_issues(self, current_context: Dict[str, Any], 
                              similarity_threshold: float = 0.7) -> List[Dict[str, Any]]:
        """
        识别历史相似期号
        
        Args:
            current_context: 当前上下文信息
            similarity_threshold: 相似度阈值
            
        Returns:
            List[Dict]: 相似期号列表
        """
        try:
            if self.historical_df.empty:
                return []
            
            similar_issues = []
            current_time = datetime.now()
            
            # 基于时间特征的相似性
            time_similar = self._find_time_similar_issues(current_time)
            
            # 基于数字模式的相似性
            pattern_similar = self._find_pattern_similar_issues(current_context)
            
            # 合并和评分
            all_similar = time_similar + pattern_similar
            
            # 去重并排序
            unique_similar = {}
            for item in all_similar:
                issue = item['issue']
                if issue not in unique_similar or item['similarity_score'] > unique_similar[issue]['similarity_score']:
                    unique_similar[issue] = item
            
            # 过滤和排序
            filtered_similar = [
                item for item in unique_similar.values() 
                if item['similarity_score'] >= similarity_threshold
            ]
            
            return sorted(filtered_similar, key=lambda x: x['similarity_score'], reverse=True)[:10]
            
        except Exception as e:
            logger.error(f"识别相似期号失败: {e}")
            return []

    def generate_evidence_based_recommendations(self, prediction_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        生成基于历史证据的推荐理由

        Args:
            prediction_context: 预测上下文

        Returns:
            List[Dict]: 推荐理由列表
        """
        try:
            recommendations = []

            # 基于频率证据的推荐
            if hasattr(self, 'frequency_patterns'):
                for position in ['hundreds', 'tens', 'units']:
                    if position in self.frequency_patterns:
                        patterns = self.frequency_patterns[position]

                        # 热号推荐
                        for number, freq in patterns['hot_numbers']:
                            recommendations.append({
                                'type': 'frequency_hot',
                                'position': position,
                                'number': number,
                                'evidence': f'历史频率{freq:.1%}，为{position}位置热号',
                                'confidence': min(0.8, freq * 5),
                                'strength': 'high' if freq > 0.15 else 'medium'
                            })

            return recommendations[:5]  # 返回前5个推荐

        except Exception as e:
            logger.error(f"生成基于证据的推荐失败: {e}")
            return []
