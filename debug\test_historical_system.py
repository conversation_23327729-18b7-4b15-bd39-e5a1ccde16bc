#!/usr/bin/env python3
"""
历史数据驱动智能预测系统综合测试
验证整个系统的功能和性能
"""

import sys
import os
from pathlib import Path
import time
import json

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_historical_adjustment_engine():
    """测试历史数据驱动调整引擎"""
    print("=== 测试历史数据驱动调整引擎 ===")
    
    try:
        from src.fusion.historical_adjustment_engine import HistoricalAdjustmentEngine
        
        engine = HistoricalAdjustmentEngine()
        print("✅ HistoricalAdjustmentEngine初始化成功")
        
        # 测试各种调整方法
        from datetime import datetime
        current_time = datetime.now()
        
        # 测试期号敏感性调整
        adj1 = engine.calculate_issue_sensitivity_adjustment(5, "2025214", "hundreds")
        print(f"  期号敏感性调整: {adj1}")
        
        # 测试频率偏差调整
        adj2 = engine.calculate_frequency_deviation_adjustment(3, "tens")
        print(f"  频率偏差调整: {adj2}")
        
        # 测试周期性调整
        adj3 = engine.calculate_cyclical_pattern_adjustment(7, current_time, "units")
        print(f"  周期性调整: {adj3}")
        
        # 测试综合调整
        context = {'current_issue': '2025214'}
        patterns = []
        adj4 = engine.get_comprehensive_adjustment(5, "hundreds", "2025214", current_time, context, patterns)
        print(f"  综合调整: {adj4}")
        
        print("✅ 历史数据驱动调整引擎测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 历史数据驱动调整引擎测试失败: {e}")
        return False

def test_prediction_traceability():
    """测试预测追溯系统"""
    print("\n=== 测试预测追溯系统 ===")
    
    try:
        from src.fusion.prediction_traceability import PredictionTraceability
        
        engine = PredictionTraceability()
        print("✅ PredictionTraceability初始化成功")
        
        # 测试预测证据生成
        test_prediction = {
            'positions': {
                'hundreds': {'recommendations': [{'number': 5, 'confidence': 0.75}]},
                'tens': {'recommendations': [{'number': 3, 'confidence': 0.68}]},
                'units': {'recommendations': [{'number': 7, 'confidence': 0.72}]}
            },
            'algorithm_version': 'v3.0.0-历史数据驱动'
        }
        
        historical_data = {'summary': '基于历史数据分析'}
        
        evidence = engine.generate_prediction_evidence(test_prediction, historical_data)
        print(f"  预测证据生成: {len(evidence)} 个字段")
        
        # 测试追溯报告
        prediction_id = "test_pred_001"
        report = engine.create_traceability_report(prediction_id)
        print(f"  追溯报告生成: {len(report)} 个字段")
        
        print("✅ 预测追溯系统测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 预测追溯系统测试失败: {e}")
        return False

def test_historical_pattern_matcher():
    """测试历史模式匹配器"""
    print("\n=== 测试历史模式匹配器 ===")
    
    try:
        from src.analysis.historical_pattern_matcher import HistoricalPatternMatcher
        
        matcher = HistoricalPatternMatcher()
        print("✅ HistoricalPatternMatcher初始化成功")
        
        # 测试相似期号识别
        current_context = {'current_time': time.time()}
        similar_issues = matcher.identify_similar_issues(current_context, 0.5)
        print(f"  相似期号识别: 找到 {len(similar_issues)} 个相似案例")
        
        # 测试基于证据的推荐
        prediction_context = {'position': 'hundreds', 'current_time': time.time()}
        recommendations = matcher.generate_evidence_based_recommendations(prediction_context)
        print(f"  证据推荐生成: {len(recommendations)} 个推荐")
        
        print("✅ 历史模式匹配器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 历史模式匹配器测试失败: {e}")
        return False

def test_smart_recommendation_engine():
    """测试智能推荐引擎"""
    print("\n=== 测试智能推荐引擎 ===")
    
    try:
        from src.fusion.smart_recommendation_engine import SmartRecommendationEngine
        
        engine = SmartRecommendationEngine()
        print("✅ SmartRecommendationEngine初始化成功")
        
        # 测试位置推荐
        for position in ['hundreds', 'tens', 'units']:
            start_time = time.time()
            result = engine.generate_position_recommendation(position)
            end_time = time.time()
            
            recommendations = result.get('recommendations', [])
            historical_evidence = result.get('historical_evidence', {})
            traceability = result.get('traceability', {})
            
            print(f"  {position}位置推荐:")
            print(f"    推荐数量: {len(recommendations)}")
            print(f"    历史证据: {len(historical_evidence)} 个字段")
            print(f"    追溯信息: {len(traceability)} 个字段")
            print(f"    响应时间: {end_time - start_time:.3f}秒")
            
            if recommendations:
                best_rec = recommendations[0]
                print(f"    最佳推荐: 数字{best_rec.get('number')} (置信度: {best_rec.get('confidence', 0):.3f})")
        
        print("✅ 智能推荐引擎测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 智能推荐引擎测试失败: {e}")
        return False

def test_deterministic_behavior():
    """测试确定性行为"""
    print("\n=== 测试确定性行为 ===")
    
    try:
        from src.fusion.smart_recommendation_engine import SmartRecommendationEngine
        
        engine = SmartRecommendationEngine()
        
        # 多次运行相同的推荐，验证结果一致性
        results = []
        for i in range(3):
            result = engine.generate_position_recommendation('hundreds')
            recommendations = result.get('recommendations', [])
            if recommendations:
                results.append(recommendations[0].get('number'))
        
        # 验证结果一致性
        if len(set(results)) == 1:
            print(f"✅ 确定性验证通过: 3次运行结果一致 (数字{results[0]})")
            return True
        else:
            print(f"❌ 确定性验证失败: 3次运行结果不一致 {results}")
            return False
        
    except Exception as e:
        print(f"❌ 确定性行为测试失败: {e}")
        return False

def test_performance():
    """测试性能"""
    print("\n=== 测试性能 ===")
    
    try:
        from src.fusion.smart_recommendation_engine import SmartRecommendationEngine
        
        engine = SmartRecommendationEngine()
        
        # 测试响应时间
        start_time = time.time()
        
        for position in ['hundreds', 'tens', 'units']:
            result = engine.generate_position_recommendation(position)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"  3个位置推荐总时间: {total_time:.3f}秒")
        print(f"  平均每个位置: {total_time/3:.3f}秒")
        
        if total_time < 10:  # 10秒内完成
            print("✅ 性能测试通过")
            return True
        else:
            print("⚠️ 性能测试警告: 响应时间较长")
            return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 历史数据驱动智能预测系统综合测试")
    print("=" * 60)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(test_historical_adjustment_engine())
    test_results.append(test_prediction_traceability())
    test_results.append(test_historical_pattern_matcher())
    test_results.append(test_smart_recommendation_engine())
    test_results.append(test_deterministic_behavior())
    test_results.append(test_performance())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果统计: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("✅ 历史数据驱动智能预测系统运行正常")
        print("✅ 零随机因子验证通过")
        print("✅ 可追溯预测体系建立完成")
        print("✅ 系统性能满足要求")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
