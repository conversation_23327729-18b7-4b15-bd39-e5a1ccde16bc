#!/usr/bin/env python3
"""
显示表结构
"""

import sqlite3

def show_table_schema():
    """显示表结构"""
    try:
        conn = sqlite3.connect("data/fucai3d.db")
        cursor = conn.cursor()
        
        # 获取final_predictions表的创建语句
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='final_predictions'")
        result = cursor.fetchone()
        
        if result:
            print("=== final_predictions表创建语句 ===")
            print(result[0])
        else:
            print("final_predictions表不存在")
        
        # 获取表结构信息
        print("\n=== final_predictions表字段信息 ===")
        cursor.execute("PRAGMA table_info(final_predictions)")
        columns = cursor.fetchall()
        
        for col in columns:
            cid, name, type_, notnull, default, pk = col
            not_null_str = "NOT NULL" if notnull else ""
            pk_str = "PRIMARY KEY" if pk else ""
            default_str = f"DEFAULT: {default}" if default else ""
            print(f"{name:25} {type_:15} {not_null_str:10} {pk_str:12} {default_str}")
        
        conn.close()
        
    except Exception as e:
        print(f"查看表结构出错: {e}")

if __name__ == "__main__":
    show_table_schema()
