#!/usr/bin/env python3
"""
零随机因子验证测试
验证SmartRecommendationEngine中不存在任何随机因子，确保预测结果完全可重现
"""

import unittest
import sys
import os
from pathlib import Path
import tempfile
import sqlite3
import json
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.fusion.smart_recommendation_engine import SmartRecommendationEngine
    from src.fusion.historical_adjustment_engine import HistoricalAdjustmentEngine
except ImportError as e:
    print(f"导入失败: {e}")
    SmartRecommendationEngine = None
    HistoricalAdjustmentEngine = None


class TestZeroRandomFactors(unittest.TestCase):
    """零随机因子测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_path = self.temp_db.name
        
        # 创建测试数据
        self._create_test_database()
        
        # 初始化推荐引擎
        if SmartRecommendationEngine:
            self.engine = SmartRecommendationEngine(self.db_path)
        else:
            self.engine = None
    
    def tearDown(self):
        """清理测试环境"""
        try:
            os.unlink(self.db_path)
        except:
            pass
    
    def _create_test_database(self):
        """创建测试数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建lottery_data表
        cursor.execute('''
            CREATE TABLE lottery_data (
                id INTEGER PRIMARY KEY,
                issue TEXT,
                draw_date TEXT,
                hundreds INTEGER,
                tens INTEGER,
                units INTEGER,
                sum_value INTEGER,
                span INTEGER,
                number_type TEXT
            )
        ''')
        
        # 插入测试数据
        test_data = []
        for i in range(100):
            issue = f"2025{i+100:03d}"
            date = f"2025-01-{(i % 30) + 1:02d}"
            hundreds = i % 10
            tens = (i + 1) % 10
            units = (i + 2) % 10
            sum_value = hundreds + tens + units
            span = max(hundreds, tens, units) - min(hundreds, tens, units)
            number_type = "组六"
            
            test_data.append((issue, date, hundreds, tens, units, sum_value, span, number_type))
        
        cursor.executemany('''
            INSERT INTO lottery_data 
            (issue, draw_date, hundreds, tens, units, sum_value, span, number_type)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', test_data)
        
        conn.commit()
        conn.close()
    
    def test_no_random_imports(self):
        """测试代码中不存在random模块导入"""
        if not SmartRecommendationEngine:
            self.skipTest("SmartRecommendationEngine不可用")
        
        # 检查SmartRecommendationEngine源码
        engine_file = project_root / "src" / "fusion" / "smart_recommendation_engine.py"
        if engine_file.exists():
            with open(engine_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查是否包含random导入
            self.assertNotIn("import random", content, "代码中不应包含'import random'")
            self.assertNotIn("from random", content, "代码中不应包含'from random'")
            
            # 检查是否包含random调用
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if 'random.' in line and not line.strip().startswith('#'):
                    self.fail(f"第{i}行包含random调用: {line.strip()}")
    
    def test_deterministic_recommendations(self):
        """测试推荐结果的确定性"""
        if not self.engine:
            self.skipTest("推荐引擎不可用")
        
        # 多次调用相同的推荐方法，结果应该完全一致
        results = []
        for _ in range(3):
            try:
                result = self.engine.generate_daily_recommendations()
                results.append(result)
            except Exception as e:
                self.fail(f"生成推荐失败: {e}")
        
        # 验证结果一致性
        self.assertEqual(len(results), 3, "应该生成3个结果")
        
        # 比较关键字段
        for i in range(1, len(results)):
            # 比较位置推荐
            for position in ['hundreds', 'tens', 'units']:
                if position in results[0].get('positions', {}) and position in results[i].get('positions', {}):
                    pos_rec_0 = results[0]['positions'][position].get('recommendations', [])
                    pos_rec_i = results[i]['positions'][position].get('recommendations', [])
                    
                    if pos_rec_0 and pos_rec_i:
                        # 比较第一个推荐的数字（应该相同）
                        self.assertEqual(
                            pos_rec_0[0].get('number'), 
                            pos_rec_i[0].get('number'),
                            f"位置{position}的推荐数字应该一致"
                        )
    
    def test_position_recommendation_deterministic(self):
        """测试位置推荐的确定性"""
        if not self.engine:
            self.skipTest("推荐引擎不可用")
        
        # 测试每个位置的推荐
        for position in ['hundreds', 'tens', 'units']:
            results = []
            for _ in range(3):
                try:
                    result = self.engine.generate_position_recommendation(position)
                    results.append(result)
                except Exception as e:
                    self.fail(f"生成{position}位置推荐失败: {e}")
            
            # 验证结果一致性
            self.assertEqual(len(results), 3, f"{position}位置应该生成3个结果")
            
            for i in range(1, len(results)):
                recommendations_0 = results[0].get('recommendations', [])
                recommendations_i = results[i].get('recommendations', [])
                
                if recommendations_0 and recommendations_i:
                    # 比较第一个推荐
                    self.assertEqual(
                        recommendations_0[0].get('number'),
                        recommendations_i[0].get('number'),
                        f"{position}位置的第一个推荐数字应该一致"
                    )
    
    def test_historical_adjustment_engine_deterministic(self):
        """测试历史调整引擎的确定性"""
        if not HistoricalAdjustmentEngine:
            self.skipTest("HistoricalAdjustmentEngine不可用")
        
        try:
            engine = HistoricalAdjustmentEngine(self.db_path)
        except Exception as e:
            self.skipTest(f"历史调整引擎初始化失败: {e}")
        
        # 测试相同输入产生相同输出
        test_cases = [
            (5, "2025214", "hundreds"),
            (3, "2025214", "tens"),
            (7, "2025214", "units")
        ]
        
        for number, issue, position in test_cases:
            results = []
            current_time = datetime(2025, 8, 13, 10, 0, 0)  # 固定时间
            
            for _ in range(3):
                try:
                    adjustment = engine.calculate_issue_sensitivity_adjustment(number, issue, position)
                    results.append(adjustment)
                except Exception as e:
                    self.fail(f"计算调整失败: {e}")
            
            # 验证结果一致性
            for i in range(1, len(results)):
                self.assertEqual(
                    results[0], results[i],
                    f"数字{number}在{position}位置的调整应该一致"
                )
    
    def test_no_random_in_fallback_recommendations(self):
        """测试备用推荐中不包含随机因子"""
        if not self.engine:
            self.skipTest("推荐引擎不可用")
        
        # 测试备用推荐
        for position in ['hundreds', 'tens', 'units']:
            results = []
            for _ in range(3):
                try:
                    result = self.engine._generate_fallback_recommendation(position)
                    results.append(result)
                except Exception as e:
                    self.fail(f"生成{position}备用推荐失败: {e}")
            
            # 验证结果一致性
            for i in range(1, len(results)):
                recommendations_0 = results[0].get('recommendations', [])
                recommendations_i = results[i].get('recommendations', [])
                
                if recommendations_0 and recommendations_i:
                    # 比较推荐数字
                    numbers_0 = [rec.get('number') for rec in recommendations_0]
                    numbers_i = [rec.get('number') for rec in recommendations_i]
                    
                    self.assertEqual(
                        numbers_0, numbers_i,
                        f"{position}位置的备用推荐数字应该一致"
                    )
    
    def test_comprehensive_zero_random_verification(self):
        """综合零随机因子验证"""
        if not self.engine:
            self.skipTest("推荐引擎不可用")
        
        print("\n=== 零随机因子综合验证 ===")
        
        # 执行多次完整推荐流程
        all_results = []
        for run in range(5):
            try:
                result = self.engine.generate_daily_recommendations()
                all_results.append(result)
                print(f"第{run+1}次运行完成")
            except Exception as e:
                self.fail(f"第{run+1}次运行失败: {e}")
        
        # 验证所有结果的一致性
        base_result = all_results[0]
        for i, result in enumerate(all_results[1:], 2):
            # 验证位置推荐一致性
            for position in ['hundreds', 'tens', 'units']:
                if (position in base_result.get('positions', {}) and 
                    position in result.get('positions', {})):
                    
                    base_recs = base_result['positions'][position].get('recommendations', [])
                    curr_recs = result['positions'][position].get('recommendations', [])
                    
                    if base_recs and curr_recs:
                        self.assertEqual(
                            base_recs[0].get('number'),
                            curr_recs[0].get('number'),
                            f"第{i}次运行的{position}位置推荐与基准不一致"
                        )
        
        print("✅ 所有运行结果完全一致，确认零随机因子")


if __name__ == '__main__':
    unittest.main(verbosity=2)
